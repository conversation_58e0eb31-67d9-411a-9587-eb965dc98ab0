#!/usr/bin/env python3
"""
Market Stall Business Tracker - Main Application Entry Point
Clean, professional Flask application for market stall business intelligence
"""

from app import create_app
import os
import webbrowser
import threading
import time

# Create Flask application instance
app = create_app()

def open_browser(port):
    """Open browser after a short delay"""
    def delayed_open():
        time.sleep(1.5)  # Wait for server to start
        webbrowser.open(f'http://localhost:{port}')
    
    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()

if __name__ == '__main__':
    # Development server configuration
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('FLASK_PORT', 5001))
    
    print(f"🚀 Starting Market Stall Tracker on port {port}")
    print(f"📊 Debug mode: {debug_mode}")
    print(f"🌐 Opening browser at: http://localhost:{port}")
    
    # Auto-open browser
    open_browser(port)
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode
    )
