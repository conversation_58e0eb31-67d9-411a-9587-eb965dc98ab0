{% extends "base.html" %}

{% block title %}Sales Entry - Market Stall Business Tracker{% endblock %}

{% block content %}
<!-- Sales Entry View -->
<div id="sales-view" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Sales Entry</h2>
            <p class="text-gray-600">Record new sales and manage transactions</p>
        </div>
        <div class="flex items-center space-x-3">
            <button onclick="syncSquareData()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Sync Square
            </button>
        </div>
    </div>

    <!-- Sales Entry Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Record New Sale</h3>
        
        <form id="sales-form" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Product Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                    <select id="product-select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">Select a product...</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Choose from existing products or create new one</p>
                </div>

                <!-- Quantity -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                    <input type="number" id="quantity" min="1" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                </div>

                <!-- Unit Price -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Unit Price</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="unit-price" step="0.01" min="0" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                    </div>
                </div>

                <!-- Location -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <select id="location-select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">Select location...</option>
                        <option value="Main Market Stall">Main Market Stall</option>
                        <option value="Weekend Farmers Market">Weekend Farmers Market</option>
                        <option value="Festival Booth">Festival Booth</option>
                        <option value="Pop-up Shop">Pop-up Shop</option>
                    </select>
                </div>

                <!-- Payment Method -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                    <select id="payment-method" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="Cash">Cash</option>
                        <option value="Card">Card</option>
                        <option value="Mobile Payment">Mobile Payment</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <!-- Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input type="date" id="sale-date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea id="sale-notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Additional notes about this sale..."></textarea>
            </div>

            <!-- Profit Preview -->
            <div id="profit-preview" class="bg-gray-50 p-4 rounded-lg hidden">
                <h4 class="font-medium text-gray-900 mb-2">Profit Preview</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">Total Revenue:</span>
                        <div id="preview-revenue" class="font-medium">$0.00</div>
                    </div>
                    <div>
                        <span class="text-gray-600">Total COGS:</span>
                        <div id="preview-cogs" class="font-medium">$0.00</div>
                    </div>
                    <div>
                        <span class="text-gray-600">Gross Profit:</span>
                        <div id="preview-profit" class="font-medium">$0.00</div>
                    </div>
                    <div>
                        <span class="text-gray-600">Profit Margin:</span>
                        <div id="preview-margin" class="font-medium">0%</div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex space-x-3">
                <button type="button" onclick="resetSalesForm()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Reset
                </button>
                <button type="submit" class="flex-1 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Record Sale
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Sales -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Recent Sales</h3>
            <div class="flex items-center space-x-3">
                <input type="text" id="sales-search" placeholder="Search sales..." class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                <select id="sales-filter-location" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    <option value="">All Locations</option>
                </select>
            </div>
        </div>
        
        <div id="recent-sales-table">
            <!-- Table will be populated by JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/sales.js') }}"></script>
{% endblock %}

{% block init_js %}
loadSalesPage();
{% endblock %}
