{% extends "base.html" %}

{% block title %}Settings - Market Stall Business Tracker{% endblock %}

{% block content %}
<!-- Settings View -->
<div id="settings-view" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Settings</h2>
            <p class="text-gray-600">Configure your market stall business tracker</p>
        </div>
        <button onclick="saveAllSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Save All Settings
        </button>
    </div>

    <!-- Square Integration Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Square Integration</h3>
                <p class="text-sm text-gray-500">Connect your Square account to sync EFTPOS sales automatically</p>
            </div>
            <div id="square-status" class="px-3 py-1 rounded-full text-sm">
                <!-- Status will be updated by JavaScript -->
            </div>
        </div>
        
        <form id="square-settings-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Application ID</label>
                    <input type="text" id="square-app-id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Your Square Application ID">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Access Token</label>
                    <input type="password" id="square-access-token" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Your Square Access Token">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Environment</label>
                <select id="square-environment" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="sandbox">Sandbox (Testing)</option>
                    <option value="production">Production (Live)</option>
                </select>
            </div>
            
            <div class="flex space-x-3">
                <button type="button" onclick="testSquareConnection()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Test Connection
                </button>
                <button type="button" onclick="syncSquareData()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Sync Data Now
                </button>
            </div>
        </form>
        
        <div id="square-test-result" class="mt-4 hidden">
            <!-- Test results will be shown here -->
        </div>
    </div>

    <!-- Business Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Business Settings</h3>
        
        <form id="business-settings-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Labor Rate ($/hour)</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="default-labor-rate" step="0.50" min="0" value="15.00" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Consignment Split</label>
                    <select id="default-consignment-split" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="0.3">30% to partner</option>
                        <option value="0.4">40% to partner</option>
                        <option value="0.5" selected>50% to partner</option>
                        <option value="0.6">60% to partner</option>
                        <option value="0.7">70% to partner</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                <input type="text" id="business-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Your Market Stall Name">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Default Locations</label>
                <textarea id="default-locations" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Main Market Stall&#10;Weekend Farmers Market&#10;Festival Booth"></textarea>
                <p class="text-xs text-gray-500 mt-1">Enter one location per line</p>
            </div>
        </form>
    </div>

    <!-- Data Management -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Data Management</h3>
        
        <div class="space-y-4">
            <!-- Database Stats -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Database Statistics</h4>
                <div id="database-stats" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <!-- Stats will be populated by JavaScript -->
                </div>
            </div>
            
            <!-- Export Options -->
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Export Data</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button onclick="exportTable('sales')" class="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                        Export Sales
                    </button>
                    <button onclick="exportTable('products')" class="px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors">
                        Export Products
                    </button>
                    <button onclick="exportTable('daily_notes')" class="px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors">
                        Export Notes
                    </button>
                    <button onclick="exportTable('settings')" class="px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors">
                        Export Settings
                    </button>
                </div>
            </div>
            
            <!-- Backup Options -->
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Backup & Restore</h4>
                <div class="flex space-x-3">
                    <button onclick="createBackup()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        Create Backup
                    </button>
                    <button onclick="showRestoreModal()" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                        Restore Backup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Advanced Settings</h3>
        
        <form id="advanced-settings-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Cache Timeout (seconds)</label>
                    <input type="number" id="cache-timeout" min="60" max="3600" value="300" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Auto-sync Interval (hours)</label>
                    <select id="auto-sync-interval" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="0">Disabled</option>
                        <option value="1" selected>Every hour</option>
                        <option value="6">Every 6 hours</option>
                        <option value="24">Daily</option>
                    </select>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <input type="checkbox" id="debug-mode" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <label for="debug-mode" class="text-sm text-gray-700">Enable debug mode (shows detailed logs)</label>
            </div>
        </form>
    </div>

    <!-- Danger Zone -->
    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-6">
        <h3 class="text-lg font-semibold text-red-900 mb-6">Danger Zone</h3>
        
        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div>
                    <h4 class="font-medium text-red-900">Clear All Cache</h4>
                    <p class="text-sm text-red-700">Clear all cached data (will reload from database)</p>
                </div>
                <button onclick="clearCache()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Clear Cache
                </button>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div>
                    <h4 class="font-medium text-red-900">Reset All Settings</h4>
                    <p class="text-sm text-red-700">Reset all settings to default values</p>
                </div>
                <button onclick="resetSettings()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Reset Settings
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/settings.js') }}"></script>
{% endblock %}

{% block init_js %}
loadSettingsPage();
{% endblock %}
