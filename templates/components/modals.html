<!-- COGS Management Modal - Preserved exactly as-is from original -->
<div id="cogs-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900">Set Product Costs</h2>
                <button onclick="closeCOGSModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-500 mt-1">Enter costs for your handmade products to track profit margins</p>
        </div>
        
        <form id="cogs-form" class="p-6 space-y-6">
            <input type="hidden" id="cogs-product-id">
            
            <!-- Product Info -->
            <div class="bg-blue-50 p-4 rounded-lg">
                <h3 class="font-medium text-blue-900" id="cogs-product-name">Product Name</h3>
                <p class="text-sm text-blue-700" id="cogs-product-category">Category</p>
            </div>
            
            <!-- Raw Materials Section -->
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm mr-2">1</span>
                    Raw Materials Cost
                </h4>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Materials (flour, yarn, wood, etc.)</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="raw-materials-cost" step="0.01" min="0" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Cost of raw materials used to make this product</p>
                </div>
            </div>
            
            <!-- Assembly Parts Section -->
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm mr-2">2</span>
                    Assembly Parts Cost
                </h4>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Pre-made parts (hardware, components, etc.)</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="assembly-parts-cost" step="0.01" min="0" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Cost of pre-made components or hardware</p>
                </div>
            </div>
            
            <!-- Labor Section -->
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm mr-2">3</span>
                    Labor Cost
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Hours to make</label>
                        <input type="number" id="labor-hours" step="0.25" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Hourly rate</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="labor-rate" step="0.50" min="0" value="15" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="15.00">
                        </div>
                    </div>
                </div>
                <p class="text-xs text-gray-500">Labor cost = Hours × Rate (calculated automatically)</p>
            </div>
            
            <!-- Other Costs Section -->
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm mr-2">4</span>
                    Other Costs (Optional)
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Packaging Cost</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="packaging-cost" step="0.01" min="0" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Overhead Cost</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="overhead-cost" step="0.01" min="0" class="pl-7 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Consignment Section -->
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center text-sm mr-2">5</span>
                    Consignment (Optional)
                </h4>
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="is-consignment" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="is-consignment" class="text-sm text-gray-700">This is a consignment product</label>
                </div>
                <div id="consignment-details" class="hidden space-y-3 pl-6 border-l-2 border-yellow-200">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name</label>
                        <input type="text" id="consignment-partner" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Partner name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Revenue Split (Partner's share)</label>
                        <select id="consignment-split" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="0.3">30% to partner</option>
                            <option value="0.4">40% to partner</option>
                            <option value="0.5" selected>50% to partner</option>
                            <option value="0.6">60% to partner</option>
                            <option value="0.7">70% to partner</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Total Cost Summary -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-between items-center text-lg font-semibold">
                    <span>Total Cost of Goods Sold (COGS):</span>
                    <span id="total-cogs" class="text-blue-600">$0.00</span>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>Materials:</span>
                        <span id="summary-materials">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Assembly:</span>
                        <span id="summary-assembly">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Labor:</span>
                        <span id="summary-labor">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Other:</span>
                        <span id="summary-other">$0.00</span>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex space-x-3">
                <button type="button" onclick="closeCOGSModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Save COGS
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Product Modal -->
<div id="add-product-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-screen overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900">Add New Product</h2>
                <button onclick="closeAddProductModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-500 mt-1">Create a new product for your inventory</p>
        </div>
        
        <form id="add-product-form" class="p-6 space-y-4">
            <!-- Product Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                <input type="text" id="product-name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter product name">
            </div>
            
            <!-- Category -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="product-category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">No category</option>
                </select>
            </div>
            
            <!-- Stock and Pricing -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Initial Stock</label>
                    <input type="number" id="initial-stock" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Reorder Level</label>
                    <input type="number" id="reorder-level" min="0" value="5" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <!-- Square Product ID -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Square Product ID (Optional)</label>
                <input type="text" id="square-product-id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Square product/variant ID">
                <p class="text-xs text-gray-500 mt-1">Link this product to Square for automatic sync</p>
            </div>
            
            <!-- Active Status -->
            <div class="flex items-center space-x-2">
                <input type="checkbox" id="product-active" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <label for="product-active" class="text-sm text-gray-700">Product is active</label>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeAddProductModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Add Product
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Product Modal -->
<div id="edit-product-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-screen overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900">Edit Product</h2>
                <button onclick="closeEditProductModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-500 mt-1">Update product information</p>
        </div>
        
        <form id="edit-product-form" class="p-6 space-y-4">
            <input type="hidden" id="edit-product-id">
            
            <!-- Product Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                <input type="text" id="edit-product-name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <!-- Category -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="edit-product-category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">No category</option>
                </select>
            </div>
            
            <!-- Stock and Pricing -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Current Stock</label>
                    <input type="number" id="edit-current-stock" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Reorder Level</label>
                    <input type="number" id="edit-reorder-level" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <!-- Square Product ID -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Square Product ID</label>
                <input type="text" id="edit-square-product-id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <!-- Active Status -->
            <div class="flex items-center space-x-2">
                <input type="checkbox" id="edit-product-active" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <label for="edit-product-active" class="text-sm text-gray-700">Product is active</label>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="closeEditProductModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Update Product
                </button>
            </div>
        </form>
    </div>
</div>
