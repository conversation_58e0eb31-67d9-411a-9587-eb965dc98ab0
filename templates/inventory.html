{% extends "base.html" %}

{% block title %}Inventory - Market Stall Business Tracker{% endblock %}

{% block content %}
<!-- Inventory View -->
<div id="inventory-view" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Inventory Management</h2>
            <p class="text-gray-600">Manage products, stock levels, and COGS tracking</p>
        </div>
        <div class="flex items-center space-x-3">
            <button onclick="showAddProductModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add Product
            </button>
            <button onclick="exportInventory()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                Export CSV
            </button>
        </div>
    </div>

    <!-- Inventory Alerts -->
    <div id="inventory-alerts-section" class="hidden">
        <div class="bg-red-50 border border-red-200 rounded-xl p-4">
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                    <p class="font-medium text-red-800">Low Stock Alert</p>
                    <p class="text-sm text-red-700" id="low-stock-message">Some products are running low on stock.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <input type="text" id="product-search" placeholder="Search products..." class="px-3 py-2 border border-gray-300 rounded-lg">
                <select id="category-filter" class="px-3 py-2 border border-gray-300 rounded-lg">
                    <option value="">All Categories</option>
                </select>
                <select id="stock-filter" class="px-3 py-2 border border-gray-300 rounded-lg">
                    <option value="">All Stock Levels</option>
                    <option value="low">Low Stock</option>
                    <option value="out">Out of Stock</option>
                    <option value="good">Good Stock</option>
                </select>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Show:</span>
                <label class="flex items-center">
                    <input type="checkbox" id="show-inactive" class="mr-2">
                    <span class="text-sm">Inactive Products</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Products & COGS Management</h3>
            <div class="text-sm text-gray-500">
                <span id="products-count">0</span> products
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Product</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Stock</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">COGS</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Profit Margin</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                </thead>
                <tbody id="products-table-body">
                    <!-- Table rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Stock Management -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Stock Updates</h3>
        
        <form id="stock-update-form" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Product</label>
                    <select id="stock-product-select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">Select product...</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Stock Change</label>
                    <input type="number" id="stock-change" placeholder="e.g., +10 or -5" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Update Stock
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Bulk Actions</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button onclick="bulkUpdateCOGS()" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                Bulk Update COGS
            </button>
            <button onclick="bulkUpdateCategories()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                Update Categories
            </button>
            <button onclick="generateStockReport()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                Stock Report
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/inventory.js') }}"></script>
{% endblock %}

{% block init_js %}
loadInventoryPage();
{% endblock %}
