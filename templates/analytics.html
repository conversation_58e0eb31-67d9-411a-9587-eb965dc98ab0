{% extends "base.html" %}

{% block title %}Analytics - Market Stall Business Tracker{% endblock %}

{% block content %}
<!-- Analytics View -->
<div id="analytics-view" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Business Analytics</h2>
            <p class="text-gray-600">Deep dive into your profit performance and trends</p>
        </div>
        <div class="flex items-center space-x-3">
            <input type="date" id="analytics-date-from" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
            <span class="text-gray-500">to</span>
            <input type="date" id="analytics-date-to" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
            <button onclick="updateAnalytics()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Update
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="analytics-summary">
        <!-- Summary cards will be populated by JavaScript -->
    </div>

    <!-- Charts Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Profit Trends -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Profit Trends</h3>
            <div class="chart-container">
                <canvas id="analytics-profit-chart"></canvas>
            </div>
        </div>

        <!-- Product Profitability -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Profitability</h3>
            <div class="chart-container">
                <canvas id="product-profitability-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Performance Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Performance</h3>
            <div id="analytics-product-table">
                <!-- Table will be populated by JavaScript -->
            </div>
        </div>

        <!-- Location Performance Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Location Performance</h3>
            <div id="analytics-location-table">
                <!-- Table will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Consignment Report -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Consignment Partner Report</h3>
        <div id="consignment-report">
            <!-- Report will be populated by JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/analytics.js') }}"></script>
{% endblock %}

{% block init_js %}
loadAnalyticsPage();
{% endblock %}
