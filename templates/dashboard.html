{% extends "base.html" %}

{% block title %}Dashboard - Market Stall Business Tracker{% endblock %}

{% block content %}
<!-- Dashboard View -->
<div id="dashboard-view" class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Business Dashboard</h2>
            <p class="text-gray-600">Track your market stall performance and profit margins</p>
        </div>
        <div class="flex items-center space-x-3">
            <select id="time-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="7">Last 7 days</option>
                <option value="30" selected>Last 30 days</option>
                <option value="90">Last 90 days</option>
            </select>
            <button onclick="refreshDashboard()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>
    
    <!-- Alert Banners -->
    <div id="square-banner" class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 hidden">
        <div class="flex items-center gap-3">
            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div class="flex-1">
                <p class="font-medium text-yellow-800">Connect Square to import EFTPOS sales automatically</p>
                <p class="text-sm text-yellow-700">Set up Square integration to sync your terminal sales data.</p>
            </div>
            <div class="flex space-x-2">
                <button onclick="showView('settings')" class="px-4 py-2 bg-yellow-600 text-white rounded-lg text-sm hover:bg-yellow-700 transition-colors">Setup Square</button>
                <button onclick="showView('sales')" class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">Add Manual Sale</button>
            </div>
        </div>
    </div>
    
    <div id="cogs-status-banner" class="bg-blue-50 border border-blue-200 rounded-xl p-4 hidden">
        <div class="flex items-center gap-3">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <p class="font-medium text-blue-800">Set product costs to track profit margins</p>
                <p class="text-sm text-blue-700">Add COGS (Cost of Goods Sold) for your handmade products to see real profit data instead of just revenue.</p>
            </div>
            <button onclick="showView('inventory')" class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">Manage COGS</button>
        </div>
    </div>

    <!-- Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="metrics-grid">
        <!-- Metrics cards will be populated by JavaScript -->
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Profit Trends Chart -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Profit Trends</h3>
                <select id="profit-chart-period" class="text-sm border border-gray-300 rounded px-2 py-1">
                    <option value="day">Daily</option>
                    <option value="week">Weekly</option>
                    <option value="month">Monthly</option>
                </select>
            </div>
            <div class="chart-container">
                <canvas id="profit-chart"></canvas>
            </div>
        </div>

        <!-- Location Performance Chart -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Location Performance</h3>
            <div class="chart-container">
                <canvas id="location-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Product Performance Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Top Products by Profit</h3>
                <p class="text-sm text-gray-500">Products ranked by total profit margin</p>
            </div>
            <button onclick="showView('analytics')" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                View Full Analytics →
            </button>
        </div>
        <div id="product-performance-table">
            <!-- Table will be populated by JavaScript -->
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <button onclick="showView('sales')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Record New Sale
                </button>
                <button onclick="syncSquareData()" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Sync Square Data
                </button>
                <button onclick="showView('inventory')" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    Manage Inventory
                </button>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Inventory Alerts</h3>
            <div id="inventory-alerts">
                <!-- Alerts will be populated by JavaScript -->
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div id="recent-activity">
                <!-- Activity will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/charts.js') }}"></script>
{% endblock %}

{% block init_js %}
loadDashboardData();
{% endblock %}
