-- Market Stall Business Tracker - Complete Database Schema
-- This script creates all tables and indexes for the application

-- Enable WAL mode for better concurrency
PRAGMA journal_mode=WAL;

-- Sales table - Core transaction data
CREATE TABLE IF NOT EXISTS sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    time TEXT NOT NULL,
    product_name TEXT NOT NULL,
    category TEXT,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    total_amount REAL NOT NULL,
    location TEXT NOT NULL,
    payment_method TEXT,
    square_transaction_id TEXT,
    notes TEXT,
    -- Profit tracking columns
    total_cogs REAL DEFAULT 0,
    gross_profit REAL DEFAULT 0,
    profit_margin REAL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table - Inventory and COGS tracking
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    category TEXT,
    cost_price REAL,
    current_stock INTEGER DEFAULT 0,
    reorder_level INTEGER DEFAULT 10,
    active BOOLEAN DEFAULT 1,
    -- Detailed COGS tracking for handmade products
    raw_materials_cost REAL DEFAULT 0,
    assembly_parts_cost REAL DEFAULT 0,
    labor_hours REAL DEFAULT 0,
    labor_rate REAL DEFAULT 15,
    overhead_cost REAL DEFAULT 0,
    packaging_cost REAL DEFAULT 0,
    -- Consignment tracking
    is_consignment BOOLEAN DEFAULT 0,
    consignment_partner TEXT,
    consignment_split REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Daily notes table - Market conditions and observations
CREATE TABLE IF NOT EXISTS daily_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL UNIQUE,
    notes TEXT NOT NULL,
    weather TEXT,
    foot_traffic TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Settings table - Application configuration
CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT,
    category TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Performance indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);
CREATE INDEX IF NOT EXISTS idx_sales_product ON sales(product_name);
CREATE INDEX IF NOT EXISTS idx_sales_location ON sales(location);
CREATE INDEX IF NOT EXISTS idx_sales_date_location ON sales(date, location);
CREATE INDEX IF NOT EXISTS idx_daily_notes_date ON daily_notes(date);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category);

-- Insert default settings
INSERT OR IGNORE INTO settings (key, value, category, description) VALUES
('default_labor_rate', '15.0', 'business', 'Default hourly labor rate for COGS calculations'),
('default_consignment_split', '0.5', 'business', 'Default consignment partner split ratio'),
('square_environment', 'sandbox', 'square', 'Square API environment (sandbox/production)'),
('auto_sync_enabled', 'true', 'square', 'Enable automatic Square data synchronization'),
('sync_interval_hours', '1', 'square', 'Hours between automatic sync attempts');
