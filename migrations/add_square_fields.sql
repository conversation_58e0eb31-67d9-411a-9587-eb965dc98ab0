-- Migration: Add Square integration fields to products table
-- Run this if you have an existing database that needs the new Square fields

-- Add Square integration columns to products table
ALTER TABLE products ADD COLUMN square_item_id TEXT;
ALTER TABLE products ADD COLUMN square_variation_id TEXT;
ALTER TABLE products ADD COLUMN sku TEXT;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_square_variation ON products(square_variation_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);

-- Update any existing products with default values if needed
-- (No need to set defaults for new fields, they can be NULL)

PRAGMA user_version = 2;
