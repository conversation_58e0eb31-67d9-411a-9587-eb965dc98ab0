# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=5000

# Database Configuration
DATABASE_PATH=market_stall.db

# Square API Configuration
SQUARE_APPLICATION_ID=your-square-app-id
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_ENVIRONMENT=sandbox

# Business Configuration
DEFAULT_LABOR_RATE=15.0
DEFAULT_CONSIGNMENT_SPLIT=0.5

# Cache Configuration
CACHE_TIMEOUT=300
