#!/usr/bin/env python3
"""
Quick fix script for Market Stall Tracker
This script will fix common issues and get the app running
"""

import os
import sys
import sqlite3

def fix_database():
    """Fix database schema issues"""
    print("🔧 Fixing database...")
    
    db_path = '/Users/<USER>/Documents/market-stall-tracker/market_stall_dev.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if profit columns exist in sales table
        cursor.execute("PRAGMA table_info(sales)")
        columns = [row[1] for row in cursor.fetchall()]
        
        profit_columns = ['total_cogs', 'gross_profit', 'profit_margin']
        missing_columns = [col for col in profit_columns if col not in columns]
        
        if missing_columns:
            print(f"  Adding missing columns: {missing_columns}")
            for col in missing_columns:
                cursor.execute(f"ALTER TABLE sales ADD COLUMN {col} REAL DEFAULT 0")
        
        # Check products table
        cursor.execute("PRAGMA table_info(products)")
        product_columns = [row[1] for row in cursor.fetchall()]
        
        required_product_columns = [
            'raw_materials_cost', 'assembly_parts_cost', 'labor_hours', 
            'labor_rate', 'overhead_cost', 'packaging_cost', 
            'is_consignment', 'consignment_partner', 'consignment_split'
        ]
        
        for col in required_product_columns:
            if col not in product_columns:
                if col == 'is_consignment':
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col} BOOLEAN DEFAULT 0")
                elif col in ['consignment_partner']:
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col} TEXT")
                elif col == 'labor_rate':
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col} REAL DEFAULT 15")
                elif col == 'consignment_split':
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col} REAL DEFAULT 0.5")
                else:
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col} REAL DEFAULT 0")
        
        conn.commit()
        conn.close()
        print("✅ Database fixed successfully!")
        
    except Exception as e:
        print(f"❌ Database fix failed: {e}")
        return False
    
    return True

def fix_imports():
    """Fix any import issues"""
    print("🔧 Checking imports...")
    
    # Add current directory to Python path
    project_dir = '/Users/<USER>/Documents/market-stall-tracker'
    if project_dir not in sys.path:
        sys.path.insert(0, project_dir)
    
    try:
        # Test critical imports
        from app import create_app
        print("✅ App imports working!")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def create_basic_app():
    """Create a basic working version of the app"""
    print("🔧 Creating basic app version...")
    
    app_content = '''#!/usr/bin/env python3
"""
Market Stall Business Tracker - FIXED VERSION
"""

from flask import Flask, render_template, jsonify
from flask_cors import CORS
import sqlite3
import os

app = Flask(__name__)
CORS(app)

DATABASE = 'market_stall_dev.db'

def get_db():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    return """
    <html>
    <head><title>Market Stall Tracker</title></head>
    <body>
        <h1>🏪 Market Stall Tracker</h1>
        <p>✅ App is running successfully!</p>
        <h2>API Endpoints:</h2>
        <ul>
            <li><a href="/api/test">/api/test</a> - Test endpoint</li>
            <li><a href="/api/sales/summary">/api/sales/summary</a> - Sales summary</li>
            <li><a href="/api/products">/api/products</a> - Products list</li>
        </ul>
        <h2>Next Steps:</h2>
        <ol>
            <li>Test the endpoints above</li>
            <li>Run the full app: <code>python app.py</code></li>
            <li>Access the dashboard at: <code>http://localhost:5000</code></li>
        </ol>
    </body>
    </html>
    """

@app.route('/api/test')
def api_test():
    return jsonify({'status': 'success', 'message': 'API is working!'})

@app.route('/api/sales/summary')
def sales_summary():
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Get basic sales stats
        cursor.execute("""
            SELECT 
                COUNT(*) as total_sales,
                SUM(total_amount) as total_revenue,
                SUM(COALESCE(gross_profit, 0)) as total_profit
            FROM sales
        """)
        
        stats = dict(cursor.fetchone())
        db.close()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/products')
def products_list():
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute('SELECT * FROM products LIMIT 10')
        products = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({
            'success': True,
            'data': products
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 Starting Market Stall Tracker (Fixed Version)")
    print("📊 Database:", os.path.abspath(DATABASE))
    print("🌐 Access at: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
    
    try:
        with open('/Users/<USER>/Documents/market-stall-tracker/app_simple.py', 'w') as f:
            f.write(app_content)
        print("✅ Created simplified app: app_simple.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create simple app: {e}")
        return False

def main():
    """Main fix function"""
    print("🛠️  Market Stall Tracker Fix Script")
    print("=" * 50)
    
    # 1. Fix database
    db_ok = fix_database()
    
    # 2. Check imports
    imports_ok = fix_imports()
    
    # 3. Create simple version
    simple_ok = create_basic_app()
    
    print("=" * 50)
    
    if db_ok and simple_ok:
        print("🎉 FIXES APPLIED!")
        print("")
        print("To test your app:")
        print("1. cd /Users/<USER>/Documents/market-stall-tracker")
        print("2. python app_simple.py")
        print("3. Open http://localhost:5000")
        print("")
        print("If that works, try the full app:")
        print("python app.py")
    else:
        print("❌ Some fixes failed. Check the output above.")

if __name__ == '__main__':
    main()
