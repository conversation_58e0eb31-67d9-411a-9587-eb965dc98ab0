#!/usr/bin/env python3
"""
Quick test to verify the sales entry page works
"""

import requests
import sys

def test_sales_page():
    """Test if the sales page loads and basic functionality works"""
    base_url = "http://localhost:5001"
    
    print("🧪 Testing Sales Entry Page...")
    
    try:
        # Test 1: Can we access the sales page?
        print("📄 Testing sales page access...")
        response = requests.get(f"{base_url}/sales/", timeout=5)
        if response.status_code == 200:
            print("✅ Sales page loads successfully")
        else:
            print(f"❌ Sales page failed: {response.status_code}")
            return False
        
        # Test 2: Can we access the sales API?
        print("🔌 Testing sales API...")
        response = requests.get(f"{base_url}/sales/api/sales", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Sales API working - found {len(data.get('data', []))} sales")
            else:
                print("⚠️ Sales API returns error but endpoint works")
        else:
            print(f"❌ Sales API failed: {response.status_code}")
        
        # Test 3: Test creating a sale
        print("💰 Testing sale creation...")
        test_sale = {
            "product_name": "Test Product",
            "quantity": 1,
            "unit_price": 10.00,
            "location": "Test Location",
            "payment_method": "Cash",
            "notes": "Test sale from automated test"
        }
        
        response = requests.post(f"{base_url}/sales/api/sales", 
                               json=test_sale, 
                               timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Sale creation works!")
                print(f"   Created sale with profit: ${data.get('data', {}).get('gross_profit', 0):.2f}")
            else:
                print(f"⚠️ Sale creation returned error: {data.get('error')}")
        else:
            print(f"❌ Sale creation failed: {response.status_code}")
        
        print("\n🎉 Sales page testing complete!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to app. Make sure it's running on http://localhost:5001")
        return False
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 Market Stall Tracker - Sales Page Test")
    print("=" * 50)
    
    success = test_sales_page()
    
    if success:
        print("\n✅ All tests passed! Sales entry page is working!")
        print("\nNext steps:")
        print("1. Open http://localhost:5001/sales/ in your browser")
        print("2. Try adding a manual sale")
        print("3. Check the profit calculation works")
        print("4. Test the Square sync button (if configured)")
    else:
        print("\n❌ Some tests failed. Check the app is running and try again.")
        sys.exit(1)
