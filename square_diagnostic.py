#!/usr/bin/env python3
"""
Square Integration Diagnostic Tool
Run this script to diagnose and fix Square inventory integration issues

Usage: python square_diagnostic.py
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def check_database_schema():
    """Check if database has required Square fields"""
    print("🔍 Checking database schema...")
    
    db_path = os.path.join(project_root, 'market_stall_dev.db')
    
    if not os.path.exists(db_path):
        print("❌ Database file not found. Run the app first to create it.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check products table structure
        cursor.execute("PRAGMA table_info(products)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_fields = ['square_variation_id', 'square_item_id', 'current_stock']
        missing_fields = [field for field in required_fields if field not in columns]
        
        if missing_fields:
            print(f"❌ Missing database fields: {missing_fields}")
            print("💡 Fix: Restart the Flask app to run database migrations")
            return False
        else:
            print("✅ Database schema is correct")
            
        # Check if any products have Square IDs
        cursor.execute("SELECT COUNT(*) as total, SUM(CASE WHEN square_variation_id IS NOT NULL THEN 1 ELSE 0 END) as mapped FROM products")
        result = cursor.fetchone()
        total_products, mapped_products = result[0], result[1]
        
        print(f"📊 Products: {total_products} total, {mapped_products} mapped to Square")
        
        if mapped_products == 0:
            print("⚠️  No products mapped to Square - need to sync catalog first")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def check_square_config():
    """Check Square API configuration"""
    print("\n🔍 Checking Square configuration...")
    
    # Check environment variables
    access_token = os.getenv('SQUARE_ACCESS_TOKEN')
    app_id = os.getenv('SQUARE_APPLICATION_ID')
    environment = os.getenv('SQUARE_ENVIRONMENT', 'sandbox')
    
    if not access_token:
        print("❌ SQUARE_ACCESS_TOKEN not set in environment")
        print("💡 Fix: Set your Square access token in .env file or environment")
        return False
    
    if not app_id:
        print("⚠️  SQUARE_APPLICATION_ID not set (optional but recommended)")
    
    print(f"✅ Square environment: {environment}")
    print(f"✅ Access token configured: {access_token[:10]}...")
    
    return True

def test_square_connection():
    """Test actual Square API connection"""
    print("\n🔍 Testing Square API connection...")
    
    try:
        # Import after adding to path
        from app.services.square_integration import SquareIntegration
        
        square = SquareIntegration()
        success, message = square.test_connection()
        
        if success:
            print(f"✅ {message}")
            return True
        else:
            print(f"❌ {message}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're running this from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_inventory_sync():
    """Test inventory sync functionality"""
    print("\n🔍 Testing inventory sync...")
    
    try:
        from app.services.square_integration import SquareIntegration
        from app.models.products import Product
        
        # Get products with Square IDs
        products = Product.get_all()
        mapped_products = [p for p in products if p.square_variation_id]
        
        if not mapped_products:
            print("⚠️  No mapped products - cannot test inventory sync")
            print("💡 Run catalog sync first: click 'Sync Catalog' in the inventory page")
            return False
        
        print(f"📦 Found {len(mapped_products)} products with Square IDs")
        
        # Test with first few products
        test_products = mapped_products[:5]
        square = SquareIntegration()
        
        variation_ids = [p.square_variation_id for p in test_products]
        inventory_data = square._batch_retrieve_inventory_counts(variation_ids)
        
        if inventory_data:
            print(f"✅ Successfully retrieved inventory for {len(inventory_data)} products")
            
            # Show sample data
            for product in test_products[:3]:
                var_id = product.square_variation_id
                square_count = inventory_data.get(var_id, 'N/A')
                local_count = product.current_stock
                print(f"   📋 {product.name}: Local={local_count}, Square={square_count}")
                
            return True
        else:
            print("❌ No inventory data returned from Square")
            print("💡 Check if products have inventory tracking enabled in Square")
            return False
            
    except Exception as e:
        print(f"❌ Inventory sync test failed: {e}")
        return False

def provide_recommendations():
    """Provide setup recommendations based on diagnostic results"""
    print("\n📋 SETUP RECOMMENDATIONS:")
    print("=" * 50)
    
    print("1. 🔧 Configure Square API:")
    print("   - Set SQUARE_ACCESS_TOKEN in your .env file")
    print("   - Use 'sandbox' for testing, 'production' for live data")
    print("   - Get tokens from: https://developer.squareup.com/")
    
    print("\n2. 📦 Sync Square Catalog:")
    print("   - Go to Inventory page in the app")
    print("   - Click 'Sync Catalog' to import products from Square")
    print("   - This creates the mapping between local and Square products")
    
    print("\n3. 📊 Sync Inventory Levels:")
    print("   - After catalog sync, click 'Sync Stock Levels'")
    print("   - This updates your local stock with Square inventory")
    print("   - Run this regularly to keep stock in sync")
    
    print("\n4. ✅ Verify Setup:")
    print("   - Check that stock levels display in inventory table")
    print("   - Verify variant grouping works correctly")
    print("   - Test COGS functionality on products")

def main():
    """Run comprehensive Square integration diagnostics"""
    print("🚀 SQUARE INTEGRATION DIAGNOSTIC TOOL")
    print("=" * 50)
    print(f"⏰ Run time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Project root: {project_root}")
    
    # Run all diagnostic checks
    checks_passed = 0
    total_checks = 4
    
    if check_database_schema():
        checks_passed += 1
    
    if check_square_config():
        checks_passed += 1
    
    if test_square_connection():
        checks_passed += 1
    
    if test_inventory_sync():
        checks_passed += 1
    
    # Summary
    print(f"\n📊 DIAGNOSTIC SUMMARY:")
    print("=" * 30)
    print(f"✅ Checks passed: {checks_passed}/{total_checks}")
    
    if checks_passed == total_checks:
        print("🎉 All systems operational! Square integration is working.")
    elif checks_passed >= 2:
        print("⚠️  Partial setup complete. Check recommendations below.")
    else:
        print("❌ Setup incomplete. Follow recommendations to fix issues.")
    
    provide_recommendations()

if __name__ == "__main__":
    main()