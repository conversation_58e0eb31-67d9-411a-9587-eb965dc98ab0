"""
Utility functions for Market Stall Business Tracker
"""

import time
from functools import wraps
from datetime import datetime, timedelta

# Simple in-memory cache
cache = {}
CACHE_TIMEOUT = 300  # 5 minutes

def cached(timeout=CACHE_TIMEOUT):
    """
    Simple caching decorator
    
    Args:
        timeout (int): Cache timeout in seconds
    """
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and args
            cache_key = f"{f.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Check if cached result exists and is not expired
            if cache_key in cache:
                result, timestamp = cache[cache_key]
                if time.time() - timestamp < timeout:
                    return result
            
            # Execute function and cache result
            result = f(*args, **kwargs)
            cache[cache_key] = (result, time.time())
            return result
        return wrapper
    return decorator

def clear_cache():
    """Clear all cached data"""
    global cache
    cache.clear()

def format_currency(amount):
    """
    Format amount as currency
    
    Args:
        amount (float): Amount to format
        
    Returns:
        str: Formatted currency string
    """
    if amount is None:
        return "$0.00"
    return f"${amount:.2f}"

def format_percentage(value):
    """
    Format value as percentage
    
    Args:
        value (float): Value to format (e.g., 0.15 for 15%)
        
    Returns:
        str: Formatted percentage string
    """
    if value is None:
        return "0.0%"
    return f"{value:.1f}%"

def calculate_date_range(period):
    """
    Calculate date range for common periods
    
    Args:
        period (str): Period name ('today', 'yesterday', 'this_week', 'last_week', 'this_month', 'last_month')
        
    Returns:
        tuple: (start_date, end_date) as strings
    """
    today = datetime.now().date()
    
    if period == 'today':
        return str(today), str(today)
    
    elif period == 'yesterday':
        yesterday = today - timedelta(days=1)
        return str(yesterday), str(yesterday)
    
    elif period == 'this_week':
        # Monday to Sunday
        start = today - timedelta(days=today.weekday())
        end = start + timedelta(days=6)
        return str(start), str(end)
    
    elif period == 'last_week':
        # Previous Monday to Sunday
        start = today - timedelta(days=today.weekday() + 7)
        end = start + timedelta(days=6)
        return str(start), str(end)
    
    elif period == 'this_month':
        start = today.replace(day=1)
        # Last day of current month
        if today.month == 12:
            end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        return str(start), str(end)
    
    elif period == 'last_month':
        # First day of last month
        if today.month == 1:
            start = today.replace(year=today.year - 1, month=12, day=1)
        else:
            start = today.replace(month=today.month - 1, day=1)
        
        # Last day of last month
        end = today.replace(day=1) - timedelta(days=1)
        return str(start), str(end)
    
    else:
        # Default to last 30 days
        start = today - timedelta(days=30)
        return str(start), str(today)

def validate_date_format(date_string):
    """
    Validate date string format (YYYY-MM-DD)
    
    Args:
        date_string (str): Date string to validate
        
    Returns:
        bool: True if valid format
    """
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def safe_float(value, default=0.0):
    """
    Safely convert value to float
    
    Args:
        value: Value to convert
        default (float): Default value if conversion fails
        
    Returns:
        float: Converted value or default
    """
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """
    Safely convert value to int
    
    Args:
        value: Value to convert
        default (int): Default value if conversion fails
        
    Returns:
        int: Converted value or default
    """
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def truncate_string(text, max_length=50):
    """
    Truncate string to maximum length
    
    Args:
        text (str): Text to truncate
        max_length (int): Maximum length
        
    Returns:
        str: Truncated text with ellipsis if needed
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."

def generate_unique_id():
    """
    Generate a unique ID based on timestamp
    
    Returns:
        str: Unique ID
    """
    import uuid
    return str(uuid.uuid4())

def parse_square_timestamp(timestamp_str):
    """
    Parse Square API timestamp format
    
    Args:
        timestamp_str (str): Square timestamp string
        
    Returns:
        datetime: Parsed datetime object
    """
    try:
        # Remove 'Z' and parse ISO format
        clean_timestamp = timestamp_str.replace('Z', '+00:00')
        return datetime.fromisoformat(clean_timestamp)
    except (ValueError, AttributeError):
        return datetime.now()

def calculate_profit_margin(revenue, cogs):
    """
    Calculate profit margin percentage
    
    Args:
        revenue (float): Total revenue
        cogs (float): Cost of goods sold
        
    Returns:
        float: Profit margin percentage
    """
    if revenue <= 0:
        return 0.0
    
    profit = revenue - cogs
    return (profit / revenue) * 100

def calculate_markup(selling_price, cost):
    """
    Calculate markup percentage
    
    Args:
        selling_price (float): Selling price
        cost (float): Cost price
        
    Returns:
        float: Markup percentage
    """
    if cost <= 0:
        return 0.0
    
    markup = selling_price - cost
    return (markup / cost) * 100
