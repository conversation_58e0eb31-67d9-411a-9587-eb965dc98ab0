"""
Settings routes for Market Stall Business Tracker
"""

from flask import Blueprint, render_template, request, jsonify
from app.models.database import get_db
from app.services.square_integration import SquareIntegration

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
def settings_page():
    """Settings page"""
    return render_template('settings.html')

@settings_bp.route('/api/settings', methods=['GET'])
def get_settings():
    """Get all settings by category"""
    try:
        category = request.args.get('category')
        
        db = get_db()
        
        if category:
            query = 'SELECT * FROM settings WHERE category = ? ORDER BY key'
            rows = db.execute(query, (category,)).fetchall()
        else:
            query = 'SELECT * FROM settings ORDER BY category, key'
            rows = db.execute(query).fetchall()
        
        settings = [dict(row) for row in rows]
        
        return jsonify({
            'success': True,
            'data': settings
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings', methods=['POST'])
def update_settings():
    """Update multiple settings"""
    try:
        data = request.json
        
        if not isinstance(data, list):
            return jsonify({
                'success': False,
                'error': 'Expected array of settings'
            }), 400
        
        db = get_db()
        
        for setting in data:
            key = setting.get('key')
            value = setting.get('value')
            category = setting.get('category', 'general')
            description = setting.get('description', '')
            
            if not key:
                continue
            
            # Insert or update setting
            db.execute('''
                INSERT OR REPLACE INTO settings (key, value, category, description, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, category, description))
        
        db.commit()
        
        return jsonify({
            'success': True,
            'message': f'Updated {len(data)} settings'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/square/test-connection', methods=['POST'])
def test_square_connection():
    """Test Square API connection"""
    try:
        square = SquareIntegration()
        success, message = square.test_connection()
        
        return jsonify({
            'success': success,
            'message': message
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/square/locations', methods=['GET'])
def get_square_locations():
    """Get Square locations"""
    try:
        square = SquareIntegration()
        
        # Test connection first
        success, message = square.test_connection()
        if not success:
            return jsonify({
                'success': False,
                'error': message
            }), 400
        
        locations = square.get_locations()
        
        return jsonify({
            'success': True,
            'data': locations
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/database/stats')
def database_stats():
    """Get database statistics"""
    try:
        db = get_db()
        
        stats = {}
        tables = ['sales', 'products', 'daily_notes', 'settings']
        
        for table in tables:
            result = db.execute(f'SELECT COUNT(*) as count FROM {table}').fetchone()
            stats[table] = result['count']
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/database/backup', methods=['POST'])
def backup_database():
    """Create database backup"""
    try:
        import shutil
        import os
        from datetime import datetime
        from flask import current_app
        
        # Get database path
        db_path = current_app.config['DATABASE_PATH']
        
        if not os.path.exists(db_path):
            return jsonify({
                'success': False,
                'error': 'Database file not found'
            }), 404
        
        # Create backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"market_stall_backup_{timestamp}.db"
        backup_path = os.path.join(os.path.dirname(db_path), backup_filename)
        
        # Copy database file
        shutil.copy2(db_path, backup_path)
        
        return jsonify({
            'success': True,
            'message': f'Database backed up to {backup_filename}',
            'backup_file': backup_filename
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/export/<table_name>')
def export_table(table_name):
    """Export table data as CSV"""
    try:
        import io
        import csv
        from flask import Response
        
        # Validate table name
        allowed_tables = ['sales', 'products', 'daily_notes', 'settings']
        if table_name not in allowed_tables:
            return jsonify({
                'success': False,
                'error': 'Invalid table name'
            }), 400
        
        db = get_db()
        rows = db.execute(f'SELECT * FROM {table_name}').fetchall()
        
        if not rows:
            return jsonify({
                'success': False,
                'error': 'No data found'
            }), 404
        
        # Create CSV output
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow(rows[0].keys())
        
        # Write data
        for row in rows:
            writer.writerow(row)
        
        # Create response
        response = Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename={table_name}_export.csv'
            }
        )
        
        return response
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/locations', methods=['GET'])
def get_all_locations():
    """Get all locations (Square + custom consignment locations)"""
    try:
        db = get_db()
        
        # Get Square locations
        square_locations = db.execute('''
            SELECT value as name, description, 'square' as type, 
                   REPLACE(key, 'square_location_', '') as location_id
            FROM settings 
            WHERE category = 'square_locations'
        ''').fetchall()
        
        # Get custom consignment locations
        custom_locations = db.execute('''
            SELECT value as name, description, 'consignment' as type,
                   REPLACE(key, 'consignment_location_', '') as location_id
            FROM settings 
            WHERE category = 'consignment_locations'
        ''').fetchall()
        
        all_locations = []
        
        # Process Square locations
        for location in square_locations:
            all_locations.append({
                'id': location['location_id'],
                'name': location['name'],
                'type': 'square',
                'description': location['description'],
                'is_consignment': False
            })
        
        # Process custom locations
        for location in custom_locations:
            # Get consignment details
            location_id = location['location_id']
            consignment_details = db.execute('''
                SELECT key, value FROM settings 
                WHERE category = 'consignment_details' 
                AND key LIKE ?
            ''', (f'%{location_id}%',)).fetchall()
            
            details = {row['key'].split('_')[-1]: row['value'] for row in consignment_details}
            
            all_locations.append({
                'id': location_id,
                'name': location['name'],
                'type': 'consignment',
                'description': location['description'],
                'is_consignment': True,
                'partner_name': details.get('partner', ''),
                'commission_type': details.get('type', 'percentage'),
                'commission_value': float(details.get('value', 0))
            })
        
        return jsonify({
            'success': True,
            'data': all_locations
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/locations', methods=['POST'])
def create_consignment_location():
    """Create new consignment location"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['name', 'partner_name', 'commission_type', 'commission_value']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Generate location ID
        import uuid
        location_id = str(uuid.uuid4())[:8]
        
        db = get_db()
        
        # Save location info
        db.execute('''
            INSERT INTO settings (key, value, category, description)
            VALUES (?, ?, 'consignment_locations', ?)
        ''', (
            f'consignment_location_{location_id}',
            data['name'],
            f"Consignment location: {data['name']}"
        ))
        
        # Save consignment details
        details = [
            (f'consignment_details_{location_id}_partner', data['partner_name']),
            (f'consignment_details_{location_id}_type', data['commission_type']),
            (f'consignment_details_{location_id}_value', str(data['commission_value']))
        ]
        
        for key, value in details:
            db.execute('''
                INSERT INTO settings (key, value, category, description)
                VALUES (?, ?, 'consignment_details', 'Consignment location details')
            ''', (key, value))
        
        db.commit()
        
        return jsonify({
            'success': True,
            'message': f'Consignment location "{data["name"]}" created successfully',
            'location_id': location_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_bp.route('/api/settings/locations/<location_id>', methods=['DELETE'])
def delete_consignment_location(location_id):
    """Delete consignment location"""
    try:
        db = get_db()
        
        # Delete location and all related settings
        db.execute('''
            DELETE FROM settings 
            WHERE (key = ? OR key LIKE ?) 
            AND category IN ('consignment_locations', 'consignment_details')
        ''', (f'consignment_location_{location_id}', f'consignment_details_{location_id}_%'))
        
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'Consignment location deleted successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
