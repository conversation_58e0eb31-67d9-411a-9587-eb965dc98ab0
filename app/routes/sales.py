"""
Sales routes for Market Stall Business Tracker
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import datetime
from app.models.sales import Sale
from app.models.products import Product
from app.services.square_integration import SquareIntegration

sales_bp = Blueprint('sales', __name__)

@sales_bp.route('/')
def sales_page():
    """Sales entry page"""
    return render_template('sales.html')

@sales_bp.route('/api/sales', methods=['GET'])
def get_sales():
    """
    Get sales records with optional filtering
    
    Query parameters:
        - limit: Number of records to return
        - offset: Number of records to skip
        - date_from: Start date filter
        - date_to: End date filter
        - location: Location filter
        - product_name: Product name filter
    """
    try:
        limit = request.args.get('limit', type=int)
        offset = request.args.get('offset', 0, type=int)
        
        filters = {}
        if request.args.get('date_from'):
            filters['date_from'] = request.args.get('date_from')
        if request.args.get('date_to'):
            filters['date_to'] = request.args.get('date_to')
        if request.args.get('location'):
            filters['location'] = request.args.get('location')
        if request.args.get('product_name'):
            filters['product_name'] = request.args.get('product_name')
        
        sales = Sale.get_all(limit=limit, offset=offset, filters=filters)
        
        return jsonify({
            'success': True,
            'data': [sale.to_dict() for sale in sales]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@sales_bp.route('/api/sales', methods=['POST'])
def create_sale():
    """Create a new sale record"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['product_name', 'quantity', 'unit_price', 'location']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Set defaults
        if 'date' not in data:
            data['date'] = datetime.now().strftime('%Y-%m-%d')
        if 'time' not in data:
            data['time'] = datetime.now().strftime('%H:%M:%S')
        
        # Calculate total amount
        total_amount = data['quantity'] * data['unit_price']
        data['total_amount'] = total_amount
        
        # Create sale object
        sale = Sale(**data)
        
        # Calculate profit if product exists
        product = Product.get_by_name(data['product_name'])
        if product:
            product_cogs = product.calculate_total_cogs()
            sale.calculate_profit(product_cogs)
            
            # Update product stock
            product.update_stock(-data['quantity'])
        
        # Save sale
        sale_id = sale.save()
        
        return jsonify({
            'success': True,
            'data': sale.to_dict(),
            'message': 'Sale created successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@sales_bp.route('/api/sales/<int:sale_id>', methods=['PUT'])
def update_sale(sale_id):
    """Update an existing sale record"""
    try:
        data = request.json
        
        # Get existing sale
        sale = Sale.get_by_id(sale_id)
        if not sale:
            return jsonify({
                'success': False,
                'error': 'Sale not found'
            }), 404
        
        # Store old quantity for stock adjustment
        old_quantity = sale.quantity
        old_product_name = sale.product_name
        
        # Update sale attributes
        for key, value in data.items():
            if hasattr(sale, key):
                setattr(sale, key, value)
        
        # Recalculate total amount if quantity or price changed
        if 'quantity' in data or 'unit_price' in data:
            sale.total_amount = sale.quantity * sale.unit_price
        
        # Recalculate profit if product exists
        product = Product.get_by_name(sale.product_name)
        if product:
            product_cogs = product.calculate_total_cogs()
            sale.calculate_profit(product_cogs)
            
            # Adjust stock if quantity or product changed
            if 'quantity' in data or 'product_name' in data:
                # Restore old stock
                if old_product_name == sale.product_name:
                    stock_change = old_quantity - sale.quantity
                else:
                    # Product changed - restore old product stock and reduce new product stock
                    old_product = Product.get_by_name(old_product_name)
                    if old_product:
                        old_product.update_stock(old_quantity)
                    stock_change = -sale.quantity
                
                product.update_stock(stock_change)
        
        # Save updated sale
        sale.save()
        
        return jsonify({
            'success': True,
            'data': sale.to_dict(),
            'message': 'Sale updated successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@sales_bp.route('/api/sales/square-sync', methods=['POST'])
def sync_square_sales():
    """Comprehensive Square data sync - products, sales, and locations"""
    try:
        # Handle case where no JSON body is sent
        try:
            data = request.get_json() or {}
        except Exception:
            data = {}
        
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # Add some debug logging
        print(f"Starting Square sync with dates: {start_date} to {end_date}")
        
        square = SquareIntegration()
        
        # Test connection first
        success, message = square.test_connection()
        if not success:
            print(f"Connection test failed: {message}")
            return jsonify({
                'success': False,
                'error': message
            }), 400
        
        print("Connection test passed, starting comprehensive sync...")
        
        # Comprehensive sync
        results = square.sync_all_data(start_date, end_date)
        
        return jsonify({
            'success': results['products_synced'] > 0 or results['orders_synced'] > 0,
            'products_synced': results['products_synced'],
            'orders_synced': results['orders_synced'],
            'locations_synced': results['locations_synced'],
            'errors': results['errors'],
            'message': results['message']
        })
        
    except Exception as e:
        print(f"Error in sync_square_sales: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@sales_bp.route('/api/sales/profit-calculator', methods=['POST'])
def calculate_profit():
    """Calculate profit for a potential sale"""
    try:
        data = request.json
        
        product_name = data.get('product_name')
        quantity = data.get('quantity', 1)
        selling_price = data.get('selling_price')
        
        if not all([product_name, selling_price]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: product_name, selling_price'
            }), 400
        
        product = Product.get_by_name(product_name)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Calculate profit metrics
        profit_analysis = product.calculate_profit_margin(selling_price)
        total_revenue = selling_price * quantity
        total_cogs = profit_analysis['total_cogs'] * quantity
        total_profit = profit_analysis['gross_profit'] * quantity
        
        return jsonify({
            'success': True,
            'data': {
                'product': product.to_dict(),
                'quantity': quantity,
                'selling_price': selling_price,
                'total_revenue': total_revenue,
                'total_cogs': total_cogs,
                'total_profit': total_profit,
                'profit_analysis': profit_analysis
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
