"""
Analytics routes for Market Stall Business Tracker
"""

from flask import Blueprint, render_template, request, jsonify
from app.services.analytics_service import AnalyticsService

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/')
def analytics_page():
    """Analytics page"""
    return render_template('analytics.html')

@analytics_bp.route('/api/analytics/profit-analysis')
def profit_analysis():
    """Get detailed profit analysis"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        location = request.args.get('location')
        
        # Get comprehensive analytics data
        summary = AnalyticsService.get_dashboard_summary(date_from, date_to, location)
        trends = AnalyticsService.get_profit_trends(date_from, date_to, location, 'day')
        
        return jsonify({
            'success': True,
            'data': {
                'summary': summary,
                'trends': trends
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analytics_bp.route('/api/analytics/product-profitability')
def product_profitability():
    """Get detailed product profitability analysis"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        location = request.args.get('location')
        
        performance = AnalyticsService.get_product_performance(
            date_from=date_from,
            date_to=date_to,
            location=location,
            limit=None  # Get all products
        )
        
        return jsonify({
            'success': True,
            'data': performance
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@analytics_bp.route('/api/analytics/consignment-report')
def consignment_report():
    """Get detailed consignment partner report"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        performance = AnalyticsService.get_consignment_performance(
            date_from=date_from,
            date_to=date_to
        )
        
        return jsonify({
            'success': True,
            'data': performance
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
