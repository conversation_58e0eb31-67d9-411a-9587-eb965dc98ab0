"""
Dashboard routes for Market Stall Business Tracker
"""

from flask import Blueprint, render_template, request, jsonify
from app.services.analytics_service import AnalyticsService

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@dashboard_bp.route('/api/dashboard/summary')
def dashboard_summary():
    """
    Get dashboard summary data
    
    Query parameters:
        - date_from: Start date (YYYY-MM-DD)
        - date_to: End date (YYYY-MM-DD)
        - location: Location filter
    """
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        location = request.args.get('location')
        
        summary = AnalyticsService.get_dashboard_summary(
            date_from=date_from,
            date_to=date_to,
            location=location
        )
        
        return jsonify({
            'success': True,
            'data': summary
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/dashboard/profit-trends')
def profit_trends():
    """
    Get profit trends over time
    
    Query parameters:
        - date_from: Start date
        - date_to: End date
        - location: Location filter
        - group_by: 'day', 'week', or 'month'
    """
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        location = request.args.get('location')
        group_by = request.args.get('group_by', 'day')
        
        trends = AnalyticsService.get_profit_trends(
            date_from=date_from,
            date_to=date_to,
            location=location,
            group_by=group_by
        )
        
        return jsonify({
            'success': True,
            'data': trends
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/dashboard/product-performance')
def product_performance():
    """Get top performing products by profit"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        location = request.args.get('location')
        limit = int(request.args.get('limit', 10))
        
        performance = AnalyticsService.get_product_performance(
            date_from=date_from,
            date_to=date_to,
            location=location,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'data': performance
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/dashboard/location-performance')
def location_performance():
    """Get performance by location"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        performance = AnalyticsService.get_location_performance(
            date_from=date_from,
            date_to=date_to
        )
        
        return jsonify({
            'success': True,
            'data': performance
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/dashboard/consignment-performance')
def consignment_performance():
    """Get consignment partner performance"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        performance = AnalyticsService.get_consignment_performance(
            date_from=date_from,
            date_to=date_to
        )
        
        return jsonify({
            'success': True,
            'data': performance
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
