"""
Inventory routes for Market Stall Business Tracker
"""

from flask import Blueprint, render_template, request, jsonify
from app.models.products import Product
from app.models.database import get_db

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
def inventory_page():
    """Inventory management page"""
    return render_template('inventory.html')

@inventory_bp.route('/api/products', methods=['GET'])
def get_products():
    """Get all products"""
    try:
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        products = Product.get_all(active_only=active_only)
        
        return jsonify({
            'success': True,
            'data': [product.to_dict() for product in products]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products', methods=['POST'])
def create_product():
    """Create a new product"""
    try:
        data = request.json
        
        # Validate required fields
        if 'name' not in data:
            return jsonify({
                'success': False,
                'error': 'Product name is required'
            }), 400
        
        # Check if product already exists
        existing = Product.get_by_name(data['name'])
        if existing:
            return jsonify({
                'success': False,
                'error': 'Product with this name already exists'
            }), 400
        
        # Create product
        product = Product(**data)
        product_id = product.save()
        
        return jsonify({
            'success': True,
            'data': product.to_dict(),
            'message': 'Product created successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """Get a single product by ID"""
    try:
        product = Product.get_by_id(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': product.to_dict()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/<int:product_id>', methods=['PUT'])
def update_product(product_id):
    """Update an existing product"""
    try:
        data = request.json
        
        # Get existing product
        product = Product.get_by_id(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Update product attributes
        for key, value in data.items():
            if hasattr(product, key):
                setattr(product, key, value)
        
        # Save updated product
        product.save()
        
        return jsonify({
            'success': True,
            'data': product.to_dict(),
            'message': 'Product updated successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/<int:product_id>/stock', methods=['POST'])
def update_stock(product_id):
    """Update product stock level"""
    try:
        data = request.json
        
        # Get product
        product = Product.get_by_id(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Validate stock change
        stock_change = data.get('stock_change')
        if stock_change is None:
            return jsonify({
                'success': False,
                'error': 'stock_change is required'
            }), 400
        
        # Update stock
        product.update_stock(stock_change)
        
        return jsonify({
            'success': True,
            'data': product.to_dict(),
            'message': f'Stock updated by {stock_change}'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/low-stock')
def get_low_stock():
    """Get products with low stock"""
    try:
        products = Product.get_low_stock()
        
        return jsonify({
            'success': True,
            'data': [product.to_dict() for product in products]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/grouped', methods=['GET'])
def get_products_grouped():
    """Get products grouped by parent/variant relationships"""
    try:
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        products = Product.get_all(active_only=active_only)
        
        # Group products by parent/variant relationships
        grouped_products = group_products_by_variants(products)
        
        return jsonify({
            'success': True,
            'data': grouped_products
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def group_products_by_variants(products):
    """Group products into parent/variant structure with smart COGS logic"""
    product_groups = []
    processed_products = set()
    
    for product in products:
        if product.id in processed_products:
            continue
            
        product_dict = product.to_dict()
        
        # Add calculated profit margin if missing
        if 'avg_profit_margin' not in product_dict:
            product_dict['avg_profit_margin'] = calculate_avg_profit_margin(product)
        
        # Check if this is a variant (contains " - ")
        if ' - ' in product.name:
            # Extract potential parent name
            parent_name = product.name.split(' - ')[0]
            
            # Look for parent product
            parent_product = None
            for p in products:
                if p.name == parent_name and p.id not in processed_products:
                    parent_product = p
                    break
            
            if parent_product:
                # This is a variant with a parent - skip for now, will be processed with parent
                continue
            else:
                # No parent found - treat as standalone with potential variants
                variants = find_variants_for_parent(products, product.name, processed_products)
                if variants:
                    # This product has variants
                    group = {
                        'parent': product_dict,
                        'variants': variants,
                        'has_variants': True,
                        'variant_count': len(variants),
                        'cogs_strategy': 'variants'  # Variants handle COGS
                    }
                    product_groups.append(group)
                    processed_products.add(product.id)
                    for v in variants:
                        processed_products.add(v['id'])
                else:
                    # Standalone product
                    group = {
                        'parent': product_dict,
                        'variants': [],
                        'has_variants': False,
                        'variant_count': 0,
                        'cogs_strategy': 'parent'  # Parent handles COGS
                    }
                    product_groups.append(group)
                    processed_products.add(product.id)
        else:
            # Check if this product has variants
            variants = find_variants_for_parent(products, product.name, processed_products)
            
            if variants:
                # This is a parent with variants
                group = {
                    'parent': product_dict,
                    'variants': variants,
                    'has_variants': True,
                    'variant_count': len(variants),
                    'cogs_strategy': 'variants'  # Variants handle COGS
                }
                product_groups.append(group)
                processed_products.add(product.id)
                for v in variants:
                    processed_products.add(v['id'])
            else:
                # Standalone product
                group = {
                    'parent': product_dict,
                    'variants': [],
                    'has_variants': False,
                    'variant_count': 0,
                    'cogs_strategy': 'parent'  # Parent handles COGS
                }
                product_groups.append(group)
                processed_products.add(product.id)
    
    return product_groups

def calculate_avg_profit_margin(product):
    """Calculate average profit margin for a product based on recent sales"""
    try:
        db = get_db()
        
        # Get recent sales for this product to calculate average profit margin
        recent_sales = db.execute('''
            SELECT AVG(profit_margin) as avg_margin 
            FROM sales 
            WHERE product_name = ? AND profit_margin > 0 
            AND date > date('now', '-90 days')
        ''', (product.name,)).fetchone()
        
        if recent_sales and recent_sales['avg_margin']:
            return round(recent_sales['avg_margin'], 1)
        
        # Fallback: calculate theoretical margin if we have COGS and a reasonable selling price
        total_cogs = product.calculate_total_cogs()
        if total_cogs > 0:
            # Use cost_price as estimated selling price if available
            estimated_price = product.cost_price or (total_cogs * 2)  # 100% markup as default
            if estimated_price > total_cogs:
                return round((estimated_price - total_cogs) / estimated_price * 100, 1)
        
        return None
        
    except Exception as e:
        print(f"Error calculating profit margin for {product.name}: {e}")
        return None

def find_variants_for_parent(products, parent_name, processed_products):
    """Find all variants for a given parent product name"""
    variants = []
    
    for product in products:
        if (product.id not in processed_products and 
            product.name.startswith(f"{parent_name} - ") and
            product.name != parent_name):
            
            variant_dict = product.to_dict()
            
            # Add calculated profit margin if missing
            if 'avg_profit_margin' not in variant_dict:
                variant_dict['avg_profit_margin'] = calculate_avg_profit_margin(product)
            
            variants.append(variant_dict)
    
    return variants
@inventory_bp.route('/api/products/categories')
def get_categories():
    """Get all product categories"""
    try:
        categories = Product.get_categories()
        
        return jsonify({
            'success': True,
            'data': categories
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/<int:product_id>', methods=['DELETE'])
def delete_product(product_id):
    """Delete a product"""
    try:
        # Get product to check if it exists
        product = Product.get_by_id(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Check if product has recent sales
        db = get_db()
        recent_sales = db.execute(
            'SELECT COUNT(*) as count FROM sales WHERE product_name = ? AND date > date("now", "-30 days")',
            (product.name,)
        ).fetchone()
        
        if recent_sales and recent_sales['count'] > 0:
            return jsonify({
                'success': False,
                'error': f'Cannot delete product with recent sales ({recent_sales["count"]} sales in last 30 days)'
            }), 400
        
        # Delete the product
        db.execute('DELETE FROM products WHERE id = ?', (product_id,))
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'Product deleted successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/force-resync-square-names', methods=['POST'])
def force_resync_square_names():
    """Force re-sync by matching product names with Square variations"""
    try:
        from app.services.square_integration import SquareIntegration
        
        square = SquareIntegration()
        
        # Test connection first
        success, message = square.test_connection()
        if not success:
            return jsonify({
                'success': False,
                'error': 'Square API connection failed: ' + message
            }), 500
        
        # Get all Square catalog items to find variations
        catalog_items = square._fetch_catalog_items()
        if not catalog_items:
            return jsonify({
                'success': False,
                'error': 'No catalog items found in Square'
            }), 500
        
        # Extract all variations with their names and IDs
        square_variations = {}
        for item in catalog_items:
            item_data = item.get('item_data', {})
            parent_name = item_data.get('name', '')
            variations = item_data.get('variations', [])
            
            for variation in variations:
                var_data = variation.get('item_variation_data', {})
                var_name = var_data.get('name', '')
                var_id = variation.get('id', '')
                
                if var_name and var_id:
                    square_variations[var_name.lower()] = {
                        'id': var_id,
                        'name': var_name,
                        'parent': parent_name,
                        'item_id': item.get('id', '')
                    }
        
        # Get all local products
        products = Product.get_all()
        matched_count = 0
        
        # Try to match local products with Square variations
        for product in products:
            # Try exact name match first
            product_name_lower = product.name.lower()
            
            if product_name_lower in square_variations:
                # Exact match found
                var_info = square_variations[product_name_lower]
                product.square_variation_id = var_info['id']
                product.square_item_id = var_info['item_id']
                product.save()
                matched_count += 1
                continue
            
            # Try partial matches for size variations
            # Look for patterns like "30ml" in "30ml - Alch. Spray"
            best_match = None
            for square_name, var_info in square_variations.items():
                # Check if the Square name contains key parts of our product name
                product_parts = product.name.lower().split(' - ')
                if len(product_parts) > 1:
                    # Extract size/variation part (like "30ml")
                    for part in product_parts:
                        if part in square_name and len(part) > 2:  # Avoid matching too short strings
                            best_match = var_info
                            break
                
                if best_match:
                    break
            
            if best_match:
                product.square_variation_id = best_match['id']
                product.square_item_id = best_match['item_id']
                product.save()
                matched_count += 1
        
        return jsonify({
            'success': True,
            'matched_count': matched_count,
            'total_square_variations': len(square_variations),
            'message': f'Matched {matched_count} products with Square variations'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/debug-square-mapping', methods=['GET'])
def debug_square_mapping():
    """Debug Square product mapping to identify sync issues"""
    try:
        from app.services.square_integration import SquareIntegration
        
        # Get all products
        products = Product.get_all()
        
        debug_info = {
            'total_products': len(products),
            'mapped_products': [],
            'unmapped_products': [],
            'square_api_test': None
        }
        
        # Check product mapping
        for product in products:
            product_info = {
                'id': product.id,
                'name': product.name,
                'square_variation_id': product.square_variation_id,
                'square_item_id': product.square_item_id,
                'current_stock': product.current_stock
            }
            
            if product.square_variation_id:
                debug_info['mapped_products'].append(product_info)
            else:
                debug_info['unmapped_products'].append(product_info)
        
        # Test Square API with mapped products
        if debug_info['mapped_products']:
            try:
                square = SquareIntegration()
                
                # Test with first few mapped products
                test_ids = [p['square_variation_id'] for p in debug_info['mapped_products'][:5]]
                inventory_data = square._batch_retrieve_inventory_counts(test_ids)
                
                debug_info['square_api_test'] = {
                    'queried_ids': test_ids,
                    'returned_data': inventory_data,
                    'success': len(inventory_data) > 0
                }
                
                # Add the actual Square data to product info
                for product_info in debug_info['mapped_products']:
                    var_id = product_info['square_variation_id']
                    product_info['square_stock'] = inventory_data.get(var_id, 'Not found')
                    
            except Exception as e:
                debug_info['square_api_test'] = {
                    'error': str(e),
                    'success': False
                }
        
        return jsonify({
            'success': True,
            'data': debug_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/check-square-setup', methods=['GET'])
def check_square_setup():
    """Check if Square integration is properly set up"""
    try:
        from app.services.square_integration import SquareIntegration
        from app.models.database import get_db
        
        # Check database schema
        db = get_db()
        cursor = db.cursor()
        cursor.execute("PRAGMA table_info(products)")
        columns = [row[1] for row in cursor.fetchall()]
        
        has_square_fields = all(col in columns for col in ['square_variation_id', 'square_item_id'])
        
        if not has_square_fields:
            return jsonify({
                'success': False,
                'error': 'Database not migrated',
                'fix': 'restart_app'
            })
        
        # Check Square connection
        square = SquareIntegration()
        success, message = square.test_connection()
        
        if not success:
            return jsonify({
                'success': False,
                'error': f'Square connection failed: {message}',
                'fix': 'configure_square'
            })
        
        # Check product mapping
        products = Product.get_all()
        mapped_products = [p for p in products if p.square_variation_id]
        
        return jsonify({
            'success': True,
            'data': {
                'square_connected': success,
                'total_products': len(products),
                'mapped_products': len(mapped_products),
                'needs_catalog_sync': len(mapped_products) == 0,
                'can_sync_inventory': len(mapped_products) > 0
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/sync-square-catalog', methods=['POST'])
def sync_square_catalog():
    """Sync product catalog from Square (must be done before inventory sync)"""
    try:
        from app.services.square_integration import SquareIntegration
        
        square = SquareIntegration()
        
        # Test connection first
        success, message = square.test_connection()
        if not success:
            return jsonify({
                'success': False,
                'error': 'Square API connection failed: ' + message
            }), 500
        
        # Sync catalog
        synced_count = square.sync_product_catalog()
        
        return jsonify({
            'success': True,
            'synced_count': synced_count,
            'message': f'Synced {synced_count} products from Square catalog'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/sync-square-stock', methods=['POST'])
def sync_square_stock():
    """Sync stock levels from Square API"""
    try:
        from app.services.square_integration import SquareIntegration
        
        square = SquareIntegration()
        
        # Test connection first
        success, message = square.test_connection()
        if not success:
            return jsonify({
                'success': False,
                'error': 'Square API connection failed: ' + message
            }), 500
        
        # Sync inventory from Square
        updated_count = square.sync_inventory_counts()
        
        return jsonify({
            'success': True,
            'updated_count': updated_count,
            'message': f'Updated stock levels for {updated_count} products'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_bp.route('/api/products/<int:product_id>/profit-analysis', methods=['POST'])
def product_profit_analysis(product_id):
    try:
        data = request.json
        selling_price = data.get('selling_price')
        
        if not selling_price:
            return jsonify({
                'success': False,
                'error': 'selling_price is required'
            }), 400
        
        # Get product
        product = Product.get_by_id(product_id)
        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404
        
        # Calculate profit analysis
        profit_analysis = product.calculate_profit_margin(selling_price)
        
        return jsonify({
            'success': True,
            'data': {
                'product': product.to_dict(),
                'selling_price': selling_price,
                'profit_analysis': profit_analysis
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
