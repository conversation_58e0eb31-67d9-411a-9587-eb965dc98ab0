"""
Database connection and initialization for Market Stall Business Tracker
"""

import sqlite3
import os
from flask import current_app, g

def get_db():
    """
    Get database connection for current request context
    
    Returns:
        sqlite3.Connection: Database connection with row factory
    """
    if 'db' not in g:
        database_path = current_app.config['DATABASE_PATH']
        g.db = sqlite3.connect(
            database_path,
            timeout=30.0,
            detect_types=sqlite3.PARSE_DECLTYPES
        )
        g.db.execute('PRAGMA journal_mode=WAL')
        g.db.row_factory = sqlite3.Row
    
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    
    if db is not None:
        db.close()

def init_db():
    """
    Initialize database with schema from migration file
    """
    database_path = current_app.config['DATABASE_PATH']
    
    # Create database directory if it doesn't exist
    db_dir = os.path.dirname(database_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    # Read and execute migration script
    migration_path = os.path.join(
        current_app.root_path, 
        '..', 
        'migrations', 
        'init_database.sql'
    )
    
    if os.path.exists(migration_path):
        with open(migration_path, 'r') as f:
            migration_sql = f.read()
        
        conn = sqlite3.connect(database_path)
        try:
            conn.executescript(migration_sql)
            conn.commit()
            print("✅ Database initialized successfully")
        except sqlite3.Error as e:
            print(f"❌ Database initialization error: {e}")
            raise
        finally:
            conn.close()
            
        # Run additional migrations for existing databases
        with current_app.app_context():
            migrate_existing_database()
    else:
        print(f"⚠️  Migration file not found: {migration_path}")

def add_column_if_not_exists(cursor, table_name, column_name, column_definition):
    """
    Add column to table if it doesn't already exist
    
    Args:
        cursor: Database cursor
        table_name (str): Name of the table
        column_name (str): Name of the column to add
        column_definition (str): SQL column definition
    """
    try:
        # Check if column exists
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        
        if column_name not in columns:
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}")
            print(f"✅ Added column {column_name} to {table_name}")
        
    except sqlite3.Error as e:
        print(f"⚠️  Error adding column {column_name} to {table_name}: {e}")

def migrate_existing_database():
    """
    Migrate existing database to add missing columns
    This function handles upgrading from the old schema
    """
    db = get_db()
    cursor = db.cursor()
    
    try:
        # Add missing profit columns to sales table
        add_column_if_not_exists(cursor, 'sales', 'total_cogs', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'sales', 'gross_profit', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'sales', 'profit_margin', 'REAL DEFAULT 0')
        
        # Add missing COGS columns to products table
        add_column_if_not_exists(cursor, 'products', 'raw_materials_cost', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'assembly_parts_cost', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'labor_hours', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'labor_rate', 'REAL DEFAULT 15')
        add_column_if_not_exists(cursor, 'products', 'overhead_cost', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'packaging_cost', 'REAL DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'is_consignment', 'BOOLEAN DEFAULT 0')
        add_column_if_not_exists(cursor, 'products', 'consignment_partner', 'TEXT')
        add_column_if_not_exists(cursor, 'products', 'consignment_split', 'REAL DEFAULT 0.5')
        
        # Add missing Square integration columns to products table
        add_column_if_not_exists(cursor, 'products', 'square_item_id', 'TEXT')
        add_column_if_not_exists(cursor, 'products', 'square_variation_id', 'TEXT')
        add_column_if_not_exists(cursor, 'products', 'sku', 'TEXT')
        
        # Create indexes for new columns if they don't exist
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_square_variation ON products(square_variation_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)')
        except sqlite3.Error:
            pass  # Index might already exist
        
        db.commit()
        print("✅ Database migration completed successfully")
        
    except sqlite3.Error as e:
        print(f"❌ Database migration error: {e}")
        db.rollback()
        raise
    finally:
        cursor.close()

def init_app(app):
    """
    Initialize database functionality with Flask app
    
    Args:
        app: Flask application instance
    """
    app.teardown_appcontext(close_db)
