"""
Sales model for Market Stall Business Tracker
"""

from datetime import datetime
from app.models.database import get_db
import sqlite3

class Sale:
    """Sales transaction model with profit calculations"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.date = kwargs.get('date')
        self.time = kwargs.get('time')
        self.product_name = kwargs.get('product_name')
        self.category = kwargs.get('category')
        self.quantity = kwargs.get('quantity', 1)
        self.unit_price = kwargs.get('unit_price', 0.0)
        self.total_amount = kwargs.get('total_amount', 0.0)
        self.location = kwargs.get('location')
        self.payment_method = kwargs.get('payment_method')
        self.square_transaction_id = kwargs.get('square_transaction_id')
        self.notes = kwargs.get('notes')
        self.total_cogs = kwargs.get('total_cogs', 0.0)
        self.gross_profit = kwargs.get('gross_profit', 0.0)
        self.profit_margin = kwargs.get('profit_margin', 0.0)
        self.created_at = kwargs.get('created_at')
    
    def calculate_profit(self, product_cogs=None):
        """
        Calculate profit metrics for this sale
        
        Args:
            product_cogs (float): Cost of goods sold per unit
        """
        if product_cogs is not None:
            self.total_cogs = product_cogs * self.quantity
            self.gross_profit = self.total_amount - self.total_cogs
            
            if self.total_amount > 0:
                self.profit_margin = (self.gross_profit / self.total_amount) * 100
            else:
                self.profit_margin = 0.0
    
    def save(self):
        """Save sale to database"""
        db = get_db()
        cursor = db.cursor()
        
        if self.id:
            # Update existing sale
            cursor.execute('''
                UPDATE sales SET
                    date=?, time=?, product_name=?, category=?, quantity=?,
                    unit_price=?, total_amount=?, location=?, payment_method=?,
                    square_transaction_id=?, notes=?, total_cogs=?,
                    gross_profit=?, profit_margin=?
                WHERE id=?
            ''', (
                self.date, self.time, self.product_name, self.category,
                self.quantity, self.unit_price, self.total_amount,
                self.location, self.payment_method, self.square_transaction_id,
                self.notes, self.total_cogs, self.gross_profit,
                self.profit_margin, self.id
            ))
        else:
            # Insert new sale
            cursor.execute('''
                INSERT INTO sales (
                    date, time, product_name, category, quantity,
                    unit_price, total_amount, location, payment_method,
                    square_transaction_id, notes, total_cogs,
                    gross_profit, profit_margin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.date, self.time, self.product_name, self.category,
                self.quantity, self.unit_price, self.total_amount,
                self.location, self.payment_method, self.square_transaction_id,
                self.notes, self.total_cogs, self.gross_profit,
                self.profit_margin
            ))
            self.id = cursor.lastrowid
        
        db.commit()
        return self.id
    
    @classmethod
    def get_by_id(cls, sale_id):
        """Get sale by ID"""
        db = get_db()
        row = db.execute('SELECT * FROM sales WHERE id = ?', (sale_id,)).fetchone()
        
        if row:
            return cls(**dict(row))
        return None
    
    @classmethod
    def get_all(cls, limit=None, offset=0, filters=None):
        """
        Get all sales with optional filtering
        
        Args:
            limit (int): Maximum number of records to return
            offset (int): Number of records to skip
            filters (dict): Filter criteria
        
        Returns:
            list: List of Sale objects
        """
        db = get_db()
        query = 'SELECT * FROM sales'
        params = []
        
        # Apply filters
        if filters:
            conditions = []
            
            if filters.get('date_from'):
                conditions.append('date >= ?')
                params.append(filters['date_from'])
            
            if filters.get('date_to'):
                conditions.append('date <= ?')
                params.append(filters['date_to'])
            
            if filters.get('location'):
                conditions.append('location = ?')
                params.append(filters['location'])
            
            if filters.get('product_name'):
                conditions.append('product_name LIKE ?')
                params.append(f"%{filters['product_name']}%")
            
            if conditions:
                query += ' WHERE ' + ' AND '.join(conditions)
        
        query += ' ORDER BY date DESC, time DESC'
        
        if limit:
            query += ' LIMIT ? OFFSET ?'
            params.extend([limit, offset])
        
        rows = db.execute(query, params).fetchall()
        return [cls(**dict(row)) for row in rows]
    
    @classmethod
    def get_summary_stats(cls, date_from=None, date_to=None, location=None):
        """
        Get summary statistics for sales
        
        Returns:
            dict: Summary statistics including revenue, profit, etc.
        """
        db = get_db()
        query = '''
            SELECT 
                COUNT(*) as total_sales,
                SUM(total_amount) as total_revenue,
                SUM(gross_profit) as total_profit,
                AVG(profit_margin) as avg_profit_margin,
                SUM(quantity) as total_items_sold
            FROM sales
        '''
        params = []
        
        conditions = []
        if date_from:
            conditions.append('date >= ?')
            params.append(date_from)
        
        if date_to:
            conditions.append('date <= ?')
            params.append(date_to)
        
        if location:
            conditions.append('location = ?')
            params.append(location)
        
        if conditions:
            query += ' WHERE ' + ' AND '.join(conditions)
        
        row = db.execute(query, params).fetchone()
        return dict(row) if row else {}
    
    def to_dict(self):
        """Convert sale to dictionary"""
        return {
            'id': self.id,
            'date': self.date,
            'time': self.time,
            'product_name': self.product_name,
            'category': self.category,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_amount': self.total_amount,
            'location': self.location,
            'payment_method': self.payment_method,
            'square_transaction_id': self.square_transaction_id,
            'notes': self.notes,
            'total_cogs': self.total_cogs,
            'gross_profit': self.gross_profit,
            'profit_margin': self.profit_margin,
            'created_at': self.created_at
        }
