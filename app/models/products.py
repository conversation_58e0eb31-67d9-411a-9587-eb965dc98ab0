"""
Products model for Market Stall Business Tracker
"""

from app.models.database import get_db
import sqlite3

class Product:
    """Product model with detailed COGS tracking"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.category = kwargs.get('category')
        self.cost_price = kwargs.get('cost_price', 0.0)
        self.current_stock = kwargs.get('current_stock', 0)
        self.reorder_level = kwargs.get('reorder_level', 10)
        self.active = kwargs.get('active', True)
        
        # Detailed COGS components
        self.raw_materials_cost = kwargs.get('raw_materials_cost', 0.0)
        self.assembly_parts_cost = kwargs.get('assembly_parts_cost', 0.0)
        self.labor_hours = kwargs.get('labor_hours', 0.0)
        self.labor_rate = kwargs.get('labor_rate', 15.0)
        self.overhead_cost = kwargs.get('overhead_cost', 0.0)
        self.packaging_cost = kwargs.get('packaging_cost', 0.0)
        
        # Consignment tracking
        self.is_consignment = kwargs.get('is_consignment', False)
        self.consignment_partner = kwargs.get('consignment_partner')
        self.consignment_split = kwargs.get('consignment_split', 0.5)
        
        # Square integration fields
        self.square_item_id = kwargs.get('square_item_id')
        self.square_variation_id = kwargs.get('square_variation_id')
        self.sku = kwargs.get('sku')
        
        self.created_at = kwargs.get('created_at')
    
    def calculate_total_cogs(self):
        """
        Calculate total cost of goods sold per unit
        
        Returns:
            float: Total COGS per unit
        """
        labor_cost = self.labor_hours * self.labor_rate
        
        total_cogs = (
            self.raw_materials_cost +
            self.assembly_parts_cost +
            labor_cost +
            self.overhead_cost +
            self.packaging_cost
        )
        
        return round(total_cogs, 2)
    
    def calculate_profit_margin(self, selling_price):
        """
        Calculate profit margin for given selling price
        
        Args:
            selling_price (float): Selling price per unit
            
        Returns:
            dict: Profit analysis including margin, markup, etc.
        """
        total_cogs = self.calculate_total_cogs()
        
        if self.is_consignment:
            # For consignment, calculate net profit after partner split
            gross_profit = selling_price - total_cogs
            partner_share = selling_price * self.consignment_split
            net_profit = gross_profit - partner_share
            
            return {
                'total_cogs': total_cogs,
                'gross_profit': gross_profit,
                'partner_share': partner_share,
                'net_profit': net_profit,
                'profit_margin': (net_profit / selling_price * 100) if selling_price > 0 else 0,
                'markup': (net_profit / total_cogs * 100) if total_cogs > 0 else 0
            }
        else:
            # Regular product profit calculation
            gross_profit = selling_price - total_cogs
            
            return {
                'total_cogs': total_cogs,
                'gross_profit': gross_profit,
                'profit_margin': (gross_profit / selling_price * 100) if selling_price > 0 else 0,
                'markup': (gross_profit / total_cogs * 100) if total_cogs > 0 else 0
            }
    
    def save(self):
        """Save product to database"""
        db = get_db()
        cursor = db.cursor()
        
        # Check if Square columns exist (for backward compatibility)
        cursor.execute("PRAGMA table_info(products)")
        columns = [row[1] for row in cursor.fetchall()]
        has_square_fields = all(col in columns for col in ['square_item_id', 'square_variation_id', 'sku'])
        
        try:
            if self.id:
                # Update existing product
                if has_square_fields:
                    cursor.execute('''
                        UPDATE products SET
                            name=?, category=?, cost_price=?, current_stock=?,
                            reorder_level=?, active=?, raw_materials_cost=?,
                            assembly_parts_cost=?, labor_hours=?, labor_rate=?,
                            overhead_cost=?, packaging_cost=?, is_consignment=?,
                            consignment_partner=?, consignment_split=?,
                            square_item_id=?, square_variation_id=?, sku=?
                        WHERE id=?
                    ''', (
                        self.name, self.category, self.cost_price, self.current_stock,
                        self.reorder_level, self.active, self.raw_materials_cost,
                        self.assembly_parts_cost, self.labor_hours, self.labor_rate,
                        self.overhead_cost, self.packaging_cost, self.is_consignment,
                        self.consignment_partner, self.consignment_split,
                        self.square_item_id, self.square_variation_id, self.sku, self.id
                    ))
                else:
                    # Fallback for databases without Square fields
                    cursor.execute('''
                        UPDATE products SET
                            name=?, category=?, cost_price=?, current_stock=?,
                            reorder_level=?, active=?, raw_materials_cost=?,
                            assembly_parts_cost=?, labor_hours=?, labor_rate=?,
                            overhead_cost=?, packaging_cost=?, is_consignment=?,
                            consignment_partner=?, consignment_split=?
                        WHERE id=?
                    ''', (
                        self.name, self.category, self.cost_price, self.current_stock,
                        self.reorder_level, self.active, self.raw_materials_cost,
                        self.assembly_parts_cost, self.labor_hours, self.labor_rate,
                        self.overhead_cost, self.packaging_cost, self.is_consignment,
                        self.consignment_partner, self.consignment_split, self.id
                    ))
            else:
                # Insert new product
                if has_square_fields:
                    cursor.execute('''
                        INSERT INTO products (
                            name, category, cost_price, current_stock, reorder_level,
                            active, raw_materials_cost, assembly_parts_cost,
                            labor_hours, labor_rate, overhead_cost, packaging_cost,
                            is_consignment, consignment_partner, consignment_split,
                            square_item_id, square_variation_id, sku
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        self.name, self.category, self.cost_price, self.current_stock,
                        self.reorder_level, self.active, self.raw_materials_cost,
                        self.assembly_parts_cost, self.labor_hours, self.labor_rate,
                        self.overhead_cost, self.packaging_cost, self.is_consignment,
                        self.consignment_partner, self.consignment_split,
                        self.square_item_id, self.square_variation_id, self.sku
                    ))
                else:
                    # Fallback for databases without Square fields
                    cursor.execute('''
                        INSERT INTO products (
                            name, category, cost_price, current_stock, reorder_level,
                            active, raw_materials_cost, assembly_parts_cost,
                            labor_hours, labor_rate, overhead_cost, packaging_cost,
                            is_consignment, consignment_partner, consignment_split
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        self.name, self.category, self.cost_price, self.current_stock,
                        self.reorder_level, self.active, self.raw_materials_cost,
                        self.assembly_parts_cost, self.labor_hours, self.labor_rate,
                        self.overhead_cost, self.packaging_cost, self.is_consignment,
                        self.consignment_partner, self.consignment_split
                    ))
                self.id = cursor.lastrowid
            
            db.commit()
            return self.id
            
        except sqlite3.Error as e:
            print(f"Error saving product: {e}")
            db.rollback()
            raise
    
    @classmethod
    def get_by_id(cls, product_id):
        """Get product by ID"""
        db = get_db()
        row = db.execute('SELECT * FROM products WHERE id = ?', (product_id,)).fetchone()
        
        if row:
            return cls(**dict(row))
        return None
    
    @classmethod
    def get_by_name(cls, name):
        """Get product by name"""
        db = get_db()
        row = db.execute('SELECT * FROM products WHERE name = ?', (name,)).fetchone()
        
        if row:
            return cls(**dict(row))
        return None
    
    @classmethod
    def get_all(cls, active_only=True):
        """
        Get all products
        
        Args:
            active_only (bool): Only return active products
            
        Returns:
            list: List of Product objects
        """
        db = get_db()
        query = 'SELECT * FROM products'
        
        if active_only:
            query += ' WHERE active = 1'
        
        query += ' ORDER BY name'
        
        rows = db.execute(query).fetchall()
        return [cls(**dict(row)) for row in rows]
    
    @classmethod
    def get_low_stock(cls):
        """Get products with stock below reorder level"""
        db = get_db()
        rows = db.execute('''
            SELECT * FROM products 
            WHERE active = 1 AND current_stock <= reorder_level
            ORDER BY current_stock ASC
        ''').fetchall()
        
        return [cls(**dict(row)) for row in rows]
    
    @classmethod
    def get_categories(cls):
        """Get all unique product categories"""
        db = get_db()
        rows = db.execute('''
            SELECT DISTINCT category FROM products 
            WHERE category IS NOT NULL AND category != ''
            ORDER BY category
        ''').fetchall()
        
        return [row['category'] for row in rows]
    
    def update_stock(self, quantity_change):
        """
        Update product stock level
        
        Args:
            quantity_change (int): Change in stock (positive for increase, negative for decrease)
        """
        self.current_stock += quantity_change
        if self.current_stock < 0:
            self.current_stock = 0
        
        db = get_db()
        db.execute(
            'UPDATE products SET current_stock = ? WHERE id = ?',
            (self.current_stock, self.id)
        )
        db.commit()
    
    def to_dict(self):
        """Convert product to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'cost_price': self.cost_price,
            'current_stock': self.current_stock,
            'reorder_level': self.reorder_level,
            'active': self.active,
            'raw_materials_cost': self.raw_materials_cost,
            'assembly_parts_cost': self.assembly_parts_cost,
            'labor_hours': self.labor_hours,
            'labor_rate': self.labor_rate,
            'overhead_cost': self.overhead_cost,
            'packaging_cost': self.packaging_cost,
            'is_consignment': self.is_consignment,
            'consignment_partner': self.consignment_partner,
            'consignment_split': self.consignment_split,
            'square_item_id': self.square_item_id,
            'square_variation_id': self.square_variation_id,
            'sku': self.sku,
            'created_at': self.created_at,
            'total_cogs': self.calculate_total_cogs()
        }
