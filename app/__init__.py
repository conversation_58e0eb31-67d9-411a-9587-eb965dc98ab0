"""
Flask Application Factory for Market Stall Business Tracker
"""

from flask import Flask
from flask_cors import CORS
import os
from config import config

def create_app(config_name=None):
    """
    Create and configure Flask application instance
    
    Args:
        config_name (str): Configuration environment name
        
    Returns:
        Flask: Configured Flask application
    """
    
    # Create Flask instance with correct template directory
    app = Flask(__name__, 
                template_folder='../templates',
                static_folder='../static')
    
    # Load configuration
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    CORS(app)
    
    # Initialize database
    from app.models.database import init_db
    with app.app_context():
        init_db()
    
    # Register blueprints
    from app.routes.dashboard import dashboard_bp
    from app.routes.sales import sales_bp
    from app.routes.analytics import analytics_bp
    from app.routes.inventory import inventory_bp
    from app.routes.settings import settings_bp
    
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(sales_bp, url_prefix='/sales')
    app.register_blueprint(analytics_bp, url_prefix='/analytics')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return {'error': 'Resource not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'error': 'Internal server error'}, 500
    
    return app
