"""
Analytics Service for Market Stall Business Tracker
Business logic for profit calculations and performance analysis
"""

from datetime import datetime, timedelta
from app.models.database import get_db
from app.models.sales import Sale
from app.models.products import Product

class AnalyticsService:
    """Service for business analytics and profit calculations"""
    
    @staticmethod
    def get_dashboard_summary(date_from=None, date_to=None, location=None):
        """
        Get comprehensive dashboard summary with profit focus
        
        Args:
            date_from (str): Start date filter
            date_to (str): End date filter
            location (str): Location filter
            
        Returns:
            dict: Dashboard summary data
        """
        # Set default date range (last 30 days)
        if not date_from:
            date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not date_to:
            date_to = datetime.now().strftime('%Y-%m-%d')
        
        # Get sales summary
        sales_summary = Sale.get_summary_stats(date_from, date_to, location)
        
        # Get product performance
        product_performance = AnalyticsService.get_product_performance(date_from, date_to, location)
        
        # Get location performance
        location_performance = AnalyticsService.get_location_performance(date_from, date_to)
        
        # Get inventory alerts
        inventory_alerts = AnalyticsService.get_inventory_alerts()
        
        return {
            'summary': sales_summary,
            'product_performance': product_performance,
            'location_performance': location_performance,
            'inventory_alerts': inventory_alerts,
            'date_range': {
                'from': date_from,
                'to': date_to
            }
        }
    
    @staticmethod
    def get_product_performance(date_from=None, date_to=None, location=None, limit=10):
        """
        Get product performance ranked by profit
        
        Returns:
            list: Product performance data sorted by profit
        """
        db = get_db()
        
        query = '''
            SELECT 
                product_name,
                category,
                COUNT(*) as sales_count,
                SUM(quantity) as total_quantity,
                SUM(total_amount) as total_revenue,
                SUM(gross_profit) as total_profit,
                AVG(profit_margin) as avg_profit_margin,
                SUM(total_cogs) as total_cogs
            FROM sales
        '''
        
        params = []
        conditions = []
        
        if date_from:
            conditions.append('date >= ?')
            params.append(date_from)
        
        if date_to:
            conditions.append('date <= ?')
            params.append(date_to)
        
        if location:
            conditions.append('location = ?')
            params.append(location)
        
        if conditions:
            query += ' WHERE ' + ' AND '.join(conditions)
        
        query += '''
            GROUP BY product_name, category
            ORDER BY total_profit DESC
        '''
        
        if limit:
            query += f' LIMIT {limit}'
        
        rows = db.execute(query, params).fetchall()
        
        return [dict(row) for row in rows]
    
    @staticmethod
    def get_location_performance(date_from=None, date_to=None):
        """
        Get performance by location
        
        Returns:
            list: Location performance data
        """
        db = get_db()
        
        query = '''
            SELECT 
                location,
                COUNT(*) as sales_count,
                SUM(total_amount) as total_revenue,
                SUM(gross_profit) as total_profit,
                AVG(profit_margin) as avg_profit_margin,
                SUM(quantity) as total_items_sold
            FROM sales
        '''
        
        params = []
        conditions = []
        
        if date_from:
            conditions.append('date >= ?')
            params.append(date_from)
        
        if date_to:
            conditions.append('date <= ?')
            params.append(date_to)
        
        if conditions:
            query += ' WHERE ' + ' AND '.join(conditions)
        
        query += '''
            GROUP BY location
            ORDER BY total_profit DESC
        '''
        
        rows = db.execute(query, params).fetchall()
        
        return [dict(row) for row in rows]
    
    @staticmethod
    def get_inventory_alerts():
        """
        Get inventory alerts for low stock products
        
        Returns:
            dict: Inventory alert data
        """
        low_stock_products = Product.get_low_stock()
        
        return {
            'low_stock_count': len(low_stock_products),
            'low_stock_products': [product.to_dict() for product in low_stock_products]
        }
    
    @staticmethod
    def get_profit_trends(date_from=None, date_to=None, location=None, group_by='day'):
        """
        Get profit trends over time
        
        Args:
            group_by (str): 'day', 'week', or 'month'
            
        Returns:
            list: Trend data points
        """
        db = get_db()
        
        # Determine date grouping
        if group_by == 'week':
            date_format = "strftime('%Y-W%W', date)"
        elif group_by == 'month':
            date_format = "strftime('%Y-%m', date)"
        else:  # day
            date_format = "date"
        
        query = f'''
            SELECT 
                {date_format} as period,
                COUNT(*) as sales_count,
                SUM(total_amount) as revenue,
                SUM(gross_profit) as profit,
                AVG(profit_margin) as avg_margin
            FROM sales
        '''
        
        params = []
        conditions = []
        
        if date_from:
            conditions.append('date >= ?')
            params.append(date_from)
        
        if date_to:
            conditions.append('date <= ?')
            params.append(date_to)
        
        if location:
            conditions.append('location = ?')
            params.append(location)
        
        if conditions:
            query += ' WHERE ' + ' AND '.join(conditions)
        
        query += f'''
            GROUP BY {date_format}
            ORDER BY period
        '''
        
        rows = db.execute(query, params).fetchall()
        
        return [dict(row) for row in rows]
    
    @staticmethod
    def calculate_product_profit_metrics(product_name, selling_price):
        """
        Calculate profit metrics for a product at given selling price
        
        Args:
            product_name (str): Name of the product
            selling_price (float): Proposed selling price
            
        Returns:
            dict: Profit analysis
        """
        product = Product.get_by_name(product_name)
        
        if not product:
            return {
                'error': 'Product not found',
                'total_cogs': 0,
                'profit_analysis': {}
            }
        
        profit_analysis = product.calculate_profit_margin(selling_price)
        
        return {
            'product': product.to_dict(),
            'selling_price': selling_price,
            'profit_analysis': profit_analysis
        }
    
    @staticmethod
    def get_consignment_performance(date_from=None, date_to=None):
        """
        Get performance analysis for consignment products
        
        Returns:
            list: Consignment partner performance data
        """
        db = get_db()
        
        # Get consignment products
        consignment_products = db.execute('''
            SELECT name, consignment_partner, consignment_split
            FROM products 
            WHERE is_consignment = 1 AND active = 1
        ''').fetchall()
        
        if not consignment_products:
            return []
        
        # Get sales data for consignment products
        product_names = [p['name'] for p in consignment_products]
        placeholders = ','.join(['?' for _ in product_names])
        
        query = f'''
            SELECT 
                s.product_name,
                p.consignment_partner,
                p.consignment_split,
                COUNT(*) as sales_count,
                SUM(s.total_amount) as total_revenue,
                SUM(s.gross_profit) as total_profit
            FROM sales s
            JOIN products p ON s.product_name = p.name
            WHERE s.product_name IN ({placeholders})
        '''
        
        params = list(product_names)
        conditions = []
        
        if date_from:
            conditions.append('s.date >= ?')
            params.append(date_from)
        
        if date_to:
            conditions.append('s.date <= ?')
            params.append(date_to)
        
        if conditions:
            query += ' AND ' + ' AND '.join(conditions)
        
        query += '''
            GROUP BY s.product_name, p.consignment_partner, p.consignment_split
            ORDER BY total_profit DESC
        '''
        
        rows = db.execute(query, params).fetchall()
        
        # Calculate partner shares
        results = []
        for row in rows:
            row_dict = dict(row)
            partner_share = row_dict['total_revenue'] * row_dict['consignment_split']
            our_share = row_dict['total_revenue'] - partner_share
            
            row_dict.update({
                'partner_share': partner_share,
                'our_share': our_share,
                'net_profit': row_dict['total_profit'] - partner_share
            })
            
            results.append(row_dict)
        
        return results
