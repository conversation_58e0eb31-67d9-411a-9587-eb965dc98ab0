"""
Simplified Square API Integration Service
Clean, reliable EFTPOS data import with clear error handling
"""

import os
import requests
from datetime import datetime, timedelta
from flask import current_app
from app.models.database import get_db
from app.models.sales import Sale
from app.models.products import Product

class SquareIntegration:
    """Simplified Square API integration focused on EFTPOS data import"""
    
    def __init__(self):
        self.load_settings()
        self.base_url = self._get_base_url()
    
    def load_settings(self):
        """Load Square settings from database and environment"""
        try:
            db = get_db()
            settings = db.execute('''
                SELECT key, value FROM settings 
                WHERE category = 'square' AND key IN ('application_id', 'access_token', 'environment')
            ''').fetchall()
            
            # Set defaults
            self.application_id = None
            self.access_token = None
            self.environment = 'sandbox'
            
            # Apply database settings
            for row in settings:
                if row['key'] == 'application_id':
                    self.application_id = row['value']
                elif row['key'] == 'access_token':
                    self.access_token = row['value']
                elif row['key'] == 'environment':
                    self.environment = row['value']
            
            # Fallback to environment variables
            if not self.application_id:
                self.application_id = current_app.config.get('SQUARE_APPLICATION_ID')
            if not self.access_token:
                self.access_token = current_app.config.get('SQUARE_ACCESS_TOKEN')
            if not self.environment:
                self.environment = current_app.config.get('SQUARE_ENVIRONMENT', 'sandbox')
                
        except Exception as e:
            print(f"Error loading Square settings: {e}")
            # Fallback to config
            self.application_id = current_app.config.get('SQUARE_APPLICATION_ID')
            self.access_token = current_app.config.get('SQUARE_ACCESS_TOKEN')
            self.environment = current_app.config.get('SQUARE_ENVIRONMENT', 'sandbox')
    
    def _get_base_url(self):
        """Get Square API base URL based on environment"""
        if self.environment == 'production':
            return 'https://connect.squareup.com'
        else:
            return 'https://connect.squareupsandbox.com'
    
    def _get_headers(self):
        """Get headers for Square API requests"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'Square-Version': '2024-07-17'
        }
    
    def test_connection(self):
        """
        Test Square API connection
        
        Returns:
            tuple: (success: bool, message: str)
        """
        if not self.access_token:
            return False, "❌ Square access token not configured"
        
        try:
            url = f"{self.base_url}/v2/locations"
            response = requests.get(url, headers=self._get_headers(), timeout=15)
            
            if response.status_code == 200:
                locations = response.json().get('locations', [])
                location_names = [loc.get('name', 'Unnamed') for loc in locations]
                return True, f"✅ Connected to Square {self.environment}! Found {len(locations)} locations: {', '.join(location_names)}"
            
            elif response.status_code == 401:
                return False, "❌ Invalid access token - please check your Square credentials"
            
            else:
                return False, f"❌ Connection failed with status {response.status_code}"
                
        except requests.exceptions.Timeout:
            return False, "❌ Connection timeout - please check your internet connection"
        except Exception as e:
            return False, f"❌ Connection error: {str(e)}"
    
    def get_locations(self):
        """
        Get Square locations
        
        Returns:
            list: List of location dictionaries
        """
        try:
            url = f"{self.base_url}/v2/locations"
            response = requests.get(url, headers=self._get_headers(), timeout=15)
            
            if response.status_code == 200:
                return response.json().get('locations', [])
            else:
                print(f"Error fetching locations: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Error fetching locations: {e}")
            return []
    
    def sync_all_data(self, start_date=None, end_date=None):
        """
        Comprehensive sync of all Square data: products, inventory, orders, and locations
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format (defaults to 3 years ago)
            end_date (str): End date in YYYY-MM-DD format (defaults to today)
            
        Returns:
            dict: Summary of sync results
        """
        try:
            # Set default date range - go back 3 years to get everything
            if not start_date:
                start_date = (datetime.now() - timedelta(days=1095)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            results = {
                'products_synced': 0,
                'inventory_synced': 0,
                'orders_synced': 0,
                'locations_synced': 0,
                'errors': []
            }
            
            # 1. Sync product catalog first
            try:
                products_count = self.sync_product_catalog()
                results['products_synced'] = products_count
            except Exception as e:
                results['errors'].append(f"Product sync error: {str(e)}")
            
            # 2. Sync inventory counts (after catalog is synced so Square IDs are populated)
            try:
                inventory_count = self.sync_inventory_counts()
                results['inventory_synced'] = inventory_count
            except Exception as e:
                results['errors'].append(f"Inventory sync error: {str(e)}")
            
            # 3. Sync Square locations
            try:
                locations_count = self.sync_square_locations()
                results['locations_synced'] = locations_count
            except Exception as e:
                results['errors'].append(f"Location sync error: {str(e)}")
            
            # 4. Sync orders (which contain actual sales data)
            try:
                orders_count = self.sync_orders(start_date, end_date)
                results['orders_synced'] = orders_count
            except Exception as e:
                results['errors'].append(f"Orders sync error: {str(e)}")
            
            # Create summary message
            total_synced = results['products_synced'] + results['orders_synced'] + results['inventory_synced']
            if total_synced > 0:
                message = f"✅ Synced {results['products_synced']} products, updated {results['inventory_synced']} stock levels, {results['orders_synced']} orders, {results['locations_synced']} locations"
            else:
                message = "⚠️ No new data found to sync"
            
            if results['errors']:
                message += f" ({len(results['errors'])} errors)"
            
            results['message'] = message
            return results
            
        except Exception as e:
            return {
                'products_synced': 0,
                'inventory_synced': 0,
                'orders_synced': 0,
                'locations_synced': 0,
                'errors': [str(e)],
                'message': f"❌ Sync failed: {str(e)}"
            }
    
    def sync_product_catalog(self):
        """
        Sync product catalog from Square to local database
        
        Returns:
            int: Number of products synced
        """
        try:
            # Fetch catalog from Square
            catalog_items = self._fetch_catalog_items()
            
            if not catalog_items:
                return 0
            
            synced_count = 0
            
            for item in catalog_items:
                if self._process_catalog_item(item):
                    synced_count += 1
            
            return synced_count
            
        except Exception as e:
            print(f"Error syncing product catalog: {e}")
            return 0
    
    def sync_square_locations(self):
        """
        Sync Square locations to local settings/database
        
        Returns:
            int: Number of locations processed
        """
        try:
            locations = self.get_locations()
            
            if not locations:
                return 0
            
            # Store locations in settings table for reference
            db = get_db()
            
            for location in locations:
                location_id = location.get('id')
                location_name = location.get('name', 'Unnamed Location')
                
                # Store as setting for reference
                db.execute('''
                    INSERT OR REPLACE INTO settings (key, value, category, description)
                    VALUES (?, ?, 'square_locations', ?)
                ''', (f"square_location_{location_id}", location_name, f"Square location: {location_name}"))
            
            db.commit()
            return len(locations)
            
        except Exception as e:
            print(f"Error syncing Square locations: {e}")
            return 0
    
    def sync_orders(self, start_date, end_date):
        """
        Sync orders (actual sales with line items) from Square
        
        Returns:
            int: Number of orders synced
        """
        try:
            # First get all locations to use in orders search
            locations = self.get_locations()
            
            if not locations:
                print("No Square locations found, cannot sync orders")
                return 0
            
            location_ids = [loc.get('id') for loc in locations if loc.get('id')]
            print(f"Syncing orders for {len(location_ids)} locations: {[loc.get('name') for loc in locations]}")
            
            orders = self._fetch_orders(start_date, end_date, location_ids)
            
            if not orders:
                return 0
            
            synced_count = 0
            location_cache = {}
            
            for order in orders:
                if self._process_order(order, location_cache):
                    synced_count += 1
            
            return synced_count
            
        except Exception as e:
            print(f"Error syncing orders: {e}")
            return 0
    
    def _fetch_catalog_items(self):
        """Fetch catalog items from Square API"""
        try:
            url = f"{self.base_url}/v2/catalog/list"
            params = {
                'types': 'ITEM',  # Only get items, not modifiers, etc.
            }
            
            all_items = []
            cursor = None
            
            while True:
                if cursor:
                    params['cursor'] = cursor
                
                response = requests.get(url, headers=self._get_headers(), params=params, timeout=30)
                
                if response.status_code != 200:
                    print(f"Error fetching catalog: {response.status_code}")
                    break
                
                data = response.json()
                objects = data.get('objects', [])
                all_items.extend(objects)
                
                cursor = data.get('cursor')
                if not cursor:
                    break
            
            return all_items
            
        except Exception as e:
            print(f"Error fetching catalog items: {e}")
            return []
    
    def _fetch_orders(self, start_date, end_date, location_ids):
        """Fetch orders from Square API with required location IDs"""
        try:
            url = f"{self.base_url}/v2/orders/search"
            
            # Build search query - simplified for better compatibility
            search_query = {
                "location_ids": location_ids,  # Move location_ids to top level
                "filter": {
                    "date_time_filter": {
                        "created_at": {
                            "start_at": f"{start_date}T00:00:00Z",
                            "end_at": f"{end_date}T23:59:59Z"
                        }
                    },
                    "state_filter": {
                        "states": ["COMPLETED"]
                    }
                },
                "sort": {
                    "sort_field": "CREATED_AT",
                    "sort_order": "DESC"
                },
                "limit": 100  # Add explicit limit
            }
            
            all_orders = []
            cursor = None
            
            while True:
                body = {"query": search_query}
                if cursor:
                    body["cursor"] = cursor
                
                print(f"Fetching orders from {start_date} to {end_date} for locations: {location_ids[:2]}{'...' if len(location_ids) > 2 else ''}")
                response = requests.post(url, headers=self._get_headers(), json=body, timeout=30)
                
                print(f"Orders API response status: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"Orders API error: {response.text}")
                    # Try alternative method - fetch payments instead of orders
                    print("Falling back to payments API...")
                    return self._fetch_payments_as_orders(start_date, end_date)
                
                data = response.json()
                orders = data.get('orders', [])
                all_orders.extend(orders)
                
                print(f"Fetched {len(orders)} orders in this batch")
                
                cursor = data.get('cursor')
                if not cursor or len(orders) == 0:
                    break
            
            print(f"Total orders fetched: {len(all_orders)}")
            return all_orders
            
        except Exception as e:
            print(f"Error fetching orders: {e}")
            # Fallback to payments API
            return self._fetch_payments_as_orders(start_date, end_date)
    
    def _fetch_payments_as_orders(self, start_date, end_date):
        """Fallback: fetch payments and convert to order-like structure"""
        try:
            print("Using payments API as fallback for orders...")
            payments = self._fetch_payments(start_date, end_date)
            
            # Convert payments to order-like structure
            orders = []
            for payment in payments:
                # Create a simplified order structure from payment
                order = {
                    'id': payment.get('id'),
                    'location_id': payment.get('location_id'),
                    'created_at': payment.get('created_at'),
                    'line_items': [{
                        'name': 'Square Payment',  # Generic name since we don't have item details
                        'quantity': '1',
                        'total_money': payment.get('amount_money', {}),
                        'uid': 'payment-item'
                    }]
                }
                orders.append(order)
            
            print(f"Converted {len(payments)} payments to order format")
            return orders
            
        except Exception as e:
            print(f"Error in fallback payments fetch: {e}")
            return []
    
    def _process_catalog_item(self, item):
        """
        Process a Square catalog item and save to local database
        Handles product variations intelligently with proper variation tracking
        
        Returns:
            bool: True if item was processed successfully
        """
        try:
            item_data = item.get('item_data', {})
            
            if not item_data:
                return False
            
            name = item_data.get('name', '').strip()
            if not name:
                return False
            
            # Get variations
            variations = item_data.get('variations', [])
            
            # Debug logging for variants
            if len(variations) > 1:
                print(f"Product '{name}' has {len(variations)} variations")
                for i, var in enumerate(variations):
                    var_data = var.get('item_variation_data', {})
                    var_name = var_data.get('name', f'Variant {i+1}')
                    var_id = var.get('id', 'no-id')
                    price = var_data.get('price_money', {}).get('amount', 0) / 100
                    print(f"  - {var_name} (ID: {var_id[-8:]}, Price: ${price:.2f})")
            
            # Always process each variation as a separate trackable item
            # This allows for proper inventory and sales tracking per variant
            return self._process_item_with_variations(name, item_data, variations, item.get('id'))
            
        except Exception as e:
            print(f"Error processing catalog item: {e}")
            return False
    
    def _process_item_with_variations(self, parent_name, item_data, variations, item_id):
        """
        Modern approach: Process each variation as a separate product for accurate tracking
        This allows proper inventory management and sales tracking per variant
        """
        try:
            if not variations:
                return False
            
            category_name = self._get_category_name(item_data.get('category_id'))
            processed_count = 0
            
            # Store item options information if available
            item_options = item_data.get('item_options', [])
            options_info = self._extract_item_options_info(item_options)
            
            for variation in variations:
                if self._process_single_variation(parent_name, variation, category_name, options_info, item_id):
                    processed_count += 1
            
            return processed_count > 0
            
        except Exception as e:
            print(f"Error processing item with variations: {e}")
            return False
    
    def _extract_item_options_info(self, item_options):
        """
        Extract meaningful option information from Square item options
        Returns a dictionary mapping option names to their possible values
        """
        options_info = {}
        
        for option in item_options:
            option_data = option.get('item_option_data', {})
            option_name = option_data.get('display_name') or option_data.get('name', '')
            
            if option_name:
                option_values = []
                for value in option_data.get('values', []):
                    value_data = value.get('item_option_value_data', {})
                    value_name = value_data.get('display_name') or value_data.get('name', '')
                    if value_name:
                        option_values.append({
                            'id': value.get('id'),
                            'name': value_name
                        })
                
                if option_values:
                    options_info[option_name] = option_values
        
        return options_info
    
    def _process_single_variation(self, parent_name, variation, category_name, options_info, item_id):
        """
        Process a single product variation as an individual product
        """
        try:
            var_data = variation.get('item_variation_data', {})
            variation_id = variation.get('id')
            
            if not variation_id:
                return False
            
            # Build variation name with option information
            variation_name = self._build_variation_name(parent_name, var_data, options_info)
            
            # Check if this variation already exists (safe column check)
            db = get_db()
            cursor = db.cursor()
            cursor.execute("PRAGMA table_info(products)")
            columns = [row[1] for row in cursor.fetchall()]
            has_square_fields = 'square_variation_id' in columns
            
            if has_square_fields:
                existing = db.execute(
                    'SELECT id FROM products WHERE name = ? OR square_variation_id = ?',
                    (variation_name, variation_id)
                ).fetchone()
            else:
                existing = db.execute(
                    'SELECT id FROM products WHERE name = ?',
                    (variation_name,)
                ).fetchone()
            
            if existing:
                # Update existing product with Square variation ID if missing and column exists
                if has_square_fields:
                    db.execute(
                        'UPDATE products SET square_variation_id = ?, square_item_id = ? WHERE id = ?',
                        (variation_id, item_id, existing['id'])
                    )
                    db.commit()
                return False  # Don't count as "new"
            
            # Create new product for this variation
            product_data = {
                'name': variation_name,
                'category': category_name,
                'active': True
            }
            
            # Get price from variation
            price_money = var_data.get('price_money', {})
            if price_money and price_money.get('amount'):
                product_data['cost_price'] = price_money.get('amount', 0) / 100
            
            # Get SKU if available
            sku = var_data.get('sku', '')
            if sku:
                product_data['sku'] = sku
            
            product = Product(**product_data)
            product.save()
            
            # Store Square variation mapping if columns exist
            if has_square_fields:
                db.execute('''
                    UPDATE products SET square_variation_id = ?, square_item_id = ? WHERE id = ?
                ''', (variation_id, item_id, product.id))
                db.commit()
            
            print(f"Created variation product: '{variation_name}' (${product_data.get('cost_price', 0):.2f})")
            return True
            
        except Exception as e:
            print(f"Error processing single variation: {e}")
            return False
    
    def _build_variation_name(self, parent_name, var_data, options_info):
        """
        Build a meaningful variation name using parent name and option values
        IMPROVED: Better handling of Square's native naming conventions
        """
        var_name = var_data.get('name', '').strip()
        
        # If variation has a custom name that's different from parent, use it directly
        # This preserves Square's naming convention
        if var_name and var_name != parent_name and var_name not in ['Regular', 'Default', 'Standard', 'Normal']:
            # Check if this looks like a standalone product name (not a size/variation)
            if not any(size in var_name.lower() for size in ['ml', 'oz', 'g', 'kg', 'l', 'small', 'medium', 'large', 'xs', 's', 'm', 'l', 'xl']):
                # Use variation name as-is (it's probably a different product)
                return var_name
            else:
                # It's a size variation - use Square's exact naming
                return var_name
        
        # Try to build name from item option values if available
        option_values = var_data.get('item_option_values', [])
        if option_values and options_info:
            option_parts = []
            
            for option_value in option_values:
                option_value_id = option_value.get('item_option_value_id')
                
                # Find the corresponding option name and value
                for option_name, values in options_info.items():
                    for value in values:
                        if value['id'] == option_value_id:
                            option_parts.append(value['name'])
                            break
            
            if option_parts:
                # For variations with clear options, use the format: "OptionValue - ParentName"
                # This matches Square's common naming pattern
                return f"{' / '.join(option_parts)} - {parent_name}"
        
        # Fallback: Use parent name with variation identifier
        if var_name and var_name != parent_name:
            return var_name  # Use Square's exact name
        
        return parent_name
    
    def _process_order(self, order, location_cache):
        """
        Process a Square order and save sales to local database
        
        Returns:
            bool: True if order was processed successfully
        """
        try:
            order_id = order.get('id')
            
            # Check if order already exists
            db = get_db()
            existing = db.execute(
                'SELECT id FROM sales WHERE square_transaction_id = ?',
                (order_id,)
            ).fetchone()
            
            if existing:
                return False  # Already synced
            
            # Extract order details
            location_id = order.get('location_id')
            location_name = self._get_location_name(location_id, location_cache)
            
            # Parse timestamp
            created_at = order.get('created_at')
            if not created_at:
                return False
            
            dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            date = dt.strftime('%Y-%m-%d')
            time = dt.strftime('%H:%M:%S')
            
            # Process line items
            line_items = order.get('line_items', [])
            if not line_items:
                return False
            
            sales_created = 0
            
            for line_item in line_items:
                if self._process_line_item(line_item, order_id, location_name, date, time):
                    sales_created += 1
            
            return sales_created > 0
            
        except Exception as e:
            print(f"Error processing order: {e}")
            return False
    
    def _process_line_item(self, line_item, order_id, location_name, date, time):
        """
        Process individual line item from Square order
        Uses catalog_object_id to match with variations for accurate tracking
        
        Returns:
            bool: True if line item was processed successfully
        """
        try:
            # Get product name
            name = line_item.get('name', '').strip()
            if not name:
                return False
            
            # Get catalog object ID (this is usually the variation ID)
            catalog_object_id = line_item.get('catalog_object_id', '')
            
            # Get quantity
            quantity = int(line_item.get('quantity', '1'))
            
            # Get pricing
            total_money = line_item.get('total_money', {})
            total_amount = total_money.get('amount', 0) / 100  # Convert from cents
            
            if total_amount <= 0:
                return False
            
            unit_price = total_amount / quantity if quantity > 0 else total_amount
            
            # Try to find matching product by Square variation ID first, then by name
            product = None
            if catalog_object_id:
                product = self._find_product_by_variation_id(catalog_object_id)
            
            if not product:
                product = Product.get_by_name(name)
            
            # If still no match, try to fetch from catalog and create/update
            if not product and catalog_object_id:
                product = self._fetch_and_create_missing_product(catalog_object_id, name)
            
            # Determine the product name to use (prefer matched product name for consistency)
            product_name = product.name if product else name
            category = product.category if product else "Square Item"
            
            # Create sale record
            sale = Sale(
                date=date,
                time=time,
                product_name=product_name,
                category=category,
                quantity=quantity,
                unit_price=unit_price,
                total_amount=total_amount,
                location=location_name,
                payment_method="Card",
                square_transaction_id=f"{order_id}-{line_item.get('uid', '')}",
                notes=f"Auto-synced from Square (catalog: {catalog_object_id[-8:] if catalog_object_id else 'none'})"
            )
            
            # Calculate profit if product exists in local database
            if product:
                product_cogs = product.calculate_total_cogs()
                sale.calculate_profit(product_cogs)
                
                # Update product stock if tracked
                if product.current_stock > 0:
                    product.update_stock(-quantity)
            
            sale.save()
            return True
            
        except Exception as e:
            print(f"Error processing line item: {e}")
            return False
    
    def _find_product_by_variation_id(self, variation_id):
        """
        Find a product by its Square variation ID
        """
        try:
            db = get_db()
            
            # Check if square_variation_id column exists
            cursor = db.cursor()
            cursor.execute("PRAGMA table_info(products)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'square_variation_id' not in columns:
                return None  # Column doesn't exist yet
            
            row = db.execute(
                'SELECT * FROM products WHERE square_variation_id = ?',
                (variation_id,)
            ).fetchone()
            
            if row:
                return Product(**dict(row))
            return None
            
        except Exception as e:
            print(f"Error finding product by variation ID: {e}")
            return None
    
    def _fetch_and_create_missing_product(self, catalog_object_id, fallback_name):
        """
        Fetch a catalog object from Square and create/update local product
        This handles cases where orders reference items not yet in local catalog
        """
        try:
            # Fetch the catalog object from Square
            url = f"{self.base_url}/v2/catalog/object/{catalog_object_id}"
            response = requests.get(url, headers=self._get_headers(), timeout=15)
            
            if response.status_code != 200:
                print(f"Could not fetch catalog object {catalog_object_id}: {response.status_code}")
                return None
            
            data = response.json()
            catalog_object = data.get('object')
            
            if not catalog_object:
                return None
            
            # If this is a variation, process it
            if catalog_object.get('type') == 'ITEM_VARIATION':
                # Try to get the parent item for context
                variation_data = catalog_object.get('item_variation_data', {})
                item_id = variation_data.get('item_id')
                
                parent_name = fallback_name
                if item_id:
                    parent_item = self._fetch_parent_item(item_id)
                    if parent_item:
                        parent_name = parent_item.get('item_data', {}).get('name', fallback_name)
                
                # Create a product for this variation
                variation_name = self._build_variation_name_from_api(
                    parent_name, catalog_object, variation_data
                )
                
                product_data = {
                    'name': variation_name,
                    'category': 'Square Item',
                    'active': True
                }
                
                # Get price if available
                price_money = variation_data.get('price_money', {})
                if price_money and price_money.get('amount'):
                    product_data['cost_price'] = price_money.get('amount', 0) / 100
                
                # Get SKU if available
                sku = variation_data.get('sku', '')
                if sku:
                    product_data['sku'] = sku
                
                product = Product(**product_data)
                product.save()
                
                # Store Square mapping if columns exist
                db = get_db()
                cursor = db.cursor()
                cursor.execute("PRAGMA table_info(products)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'square_variation_id' in columns:
                    db.execute('''
                        UPDATE products SET square_variation_id = ?, square_item_id = ? WHERE id = ?
                    ''', (catalog_object_id, item_id, product.id))
                    db.commit()
                
                print(f"Created missing product from Square catalog: '{variation_name}'")
                return product
            
            return None
            
        except Exception as e:
            print(f"Error fetching and creating missing product: {e}")
            return None
    
    def _fetch_parent_item(self, item_id):
        """
        Fetch parent item details from Square catalog
        """
        try:
            url = f"{self.base_url}/v2/catalog/object/{item_id}"
            response = requests.get(url, headers=self._get_headers(), timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('object')
            
            return None
            
        except Exception as e:
            print(f"Error fetching parent item: {e}")
            return None
    
    def _build_variation_name_from_api(self, parent_name, catalog_object, variation_data):
        """
        Build variation name from API response data
        """
        var_name = variation_data.get('name', '').strip()
        
        # If variation has a meaningful name, use it
        if var_name and var_name != parent_name:
            if not any(word in var_name.lower() for word in ['regular', 'default', 'standard', 'normal']):
                return f"{parent_name} - {var_name}"
        
        # Try to extract option values
        option_values = variation_data.get('item_option_values', [])
        if option_values:
            # This would require additional API calls to get option details
            # For now, just use the variation name if available
            if var_name and var_name != parent_name:
                return f"{parent_name} - {var_name}"
        
        return parent_name
    
    def _get_category_name(self, category_id):
        """
        Get category name from Square (simplified for now)
        
        Returns:
            str: Category name or None
        """
        if not category_id:
            return None
        
        # For now, just return the category ID
        # Could be enhanced to fetch actual category names from Square
        return f"Square Category {category_id[-6:]}"
    
    
    # Keep the old sync_payments method for backward compatibility
    def sync_payments(self, start_date=None, end_date=None, location_id=None):
        """
        Legacy method - use sync_all_data instead
        """
        result = self.sync_all_data(start_date, end_date)
        return result['orders_synced'], result['message']
    
    def _fetch_payments(self, start_date, end_date, location_id=None):
        """Fetch payments from Square API"""
        try:
            url = f"{self.base_url}/v2/payments"
            params = {
                'begin_time': f"{start_date}T00:00:00Z",
                'end_time': f"{end_date}T23:59:59Z",
                'sort_order': 'DESC',
                'limit': 200
            }
            
            if location_id:
                params['location_id'] = location_id
            
            all_payments = []
            cursor = None
            
            # Handle pagination
            while True:
                if cursor:
                    params['cursor'] = cursor
                
                response = requests.get(url, headers=self._get_headers(), params=params, timeout=30)
                
                if response.status_code != 200:
                    print(f"Error fetching payments: {response.status_code}")
                    break
                
                data = response.json()
                payments = data.get('payments', [])
                all_payments.extend(payments)
                
                cursor = data.get('cursor')
                if not cursor or len(payments) == 0:
                    break
            
            return all_payments
            
        except Exception as e:
            print(f"Error fetching payments: {e}")
            return []
    
    def _process_payment(self, payment, location_cache):
        """
        Process a single payment and save to database
        
        Returns:
            bool: True if payment was processed successfully
        """
        try:
            # Only process completed payments
            status = payment.get('status', '')
            if status not in ['COMPLETED', 'APPROVED']:
                return False
            
            payment_id = payment.get('id')
            
            # Check if payment already exists
            db = get_db()
            existing = db.execute(
                'SELECT id FROM sales WHERE square_transaction_id = ?',
                (payment_id,)
            ).fetchone()
            
            if existing:
                return False  # Already synced
            
            # Extract payment details
            location_id = payment.get('location_id')
            location_name = self._get_location_name(location_id, location_cache)
            
            # Parse timestamp
            created_at = payment.get('created_at')
            if not created_at:
                return False
            
            dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            date = dt.strftime('%Y-%m-%d')
            time = dt.strftime('%H:%M:%S')
            
            # Extract amount
            amount_money = payment.get('amount_money', {})
            total_amount = amount_money.get('amount', 0) / 100  # Convert from cents
            
            if total_amount <= 0:
                return False
            
            # Create sale record
            sale = Sale(
                date=date,
                time=time,
                product_name="Square Payment",  # Generic for EFTPOS payments
                category="Payment",
                quantity=1,
                unit_price=total_amount,
                total_amount=total_amount,
                location=location_name,
                payment_method="Card",
                square_transaction_id=payment_id,
                notes=f"Auto-synced EFTPOS payment"
            )
            
            sale.save()
            return True
            
        except Exception as e:
            print(f"Error processing payment: {e}")
            return False
    
    def sync_inventory_counts(self):
        """
        Sync inventory counts from Square API
        Now includes automatic mapping for products missing Square variation IDs
        
        Returns:
            int: Number of products updated
        """
        try:
            print("🔄 Syncing inventory counts from Square...")
            
            # Get all our products
            products = Product.get_all()
            if not products:
                print("ℹ️  No products found in database")
                return 0
            
            # Separate products with and without Square IDs
            products_with_square_ids = [p for p in products if p.square_variation_id]
            products_without_square_ids = [p for p in products if not p.square_variation_id]
            
            print(f"📊 Found {len(products)} total products: {len(products_with_square_ids)} mapped, {len(products_without_square_ids)} unmapped")
            
            # Try to map unmapped products by matching with Square catalog
            if products_without_square_ids:
                print(f"🔍 Attempting to map {len(products_without_square_ids)} unmapped products...")
                newly_mapped = self._attempt_product_mapping(products_without_square_ids)
                if newly_mapped > 0:
                    print(f"✅ Successfully mapped {newly_mapped} additional products")
                    # Refresh the list of products with Square IDs
                    products_with_square_ids = [p for p in Product.get_all() if p.square_variation_id]
            
            if not products_with_square_ids:
                print("❌ No products with Square variation IDs found. Run catalog sync first.")
                return 0
            
            updated_count = 0
            
            # Batch fetch inventory counts from Square
            catalog_object_ids = [p.square_variation_id for p in products_with_square_ids]
            print(f"📥 Fetching inventory data for {len(catalog_object_ids)} products from Square...")
            inventory_counts = self._batch_retrieve_inventory_counts(catalog_object_ids)
            
            if not inventory_counts:
                print("⚠️  No inventory data returned from Square API")
                return 0
            
            print(f"📊 Received inventory data for {len(inventory_counts)} products")
            
            # Update our products with the Square inventory counts
            for product in products_with_square_ids:
                variation_id = product.square_variation_id

                if variation_id in inventory_counts:
                    square_count = inventory_counts[variation_id]

                    # Update the product's stock if it's different
                    if product.current_stock != square_count:
                        print(f"📦 Updating {product.name}: {product.current_stock} → {square_count}")
                        product.current_stock = square_count
                        product.save()
                        updated_count += 1
                else:
                    print(f"⚠️  No inventory data found for {product.name} (ID: {variation_id[-8:] if len(variation_id) > 8 else variation_id}) - likely inventory tracking disabled")
            
            # Report unmapped products
            if products_without_square_ids:
                unmapped_after_attempt = [p for p in Product.get_all() if not p.square_variation_id]
                if unmapped_after_attempt:
                    print(f"⚠️  {len(unmapped_after_attempt)} products still unmapped to Square:")
                    for product in unmapped_after_attempt[:5]:  # Show first 5
                        print(f"   • {product.name}")
                    if len(unmapped_after_attempt) > 5:
                        print(f"   ... and {len(unmapped_after_attempt) - 5} more")
            
            print(f"✅ Updated inventory for {updated_count} products")
            return updated_count
            
        except Exception as e:
            print(f"Error syncing inventory counts: {e}")
            return 0
    
    def _batch_retrieve_inventory_counts(self, catalog_object_ids):
        """
        Batch retrieve inventory counts from Square API
        
        Args:
            catalog_object_ids (list): List of catalog object IDs (variation IDs)
            
        Returns:
            dict: Mapping of catalog_object_id to inventory count
        """
        try:
            if not catalog_object_ids:
                print("⚠️  No catalog object IDs provided for inventory lookup")
                return {}
            
            url = f"{self.base_url}/v2/inventory/batch-retrieve-counts"
            
            # Square API accepts up to 100 catalog object IDs per request
            inventory_counts = {}
            total_batches = (len(catalog_object_ids) + 99) // 100
            
            print(f"📡 Processing {len(catalog_object_ids)} products in {total_batches} batch(es)...")
            
            # Process in batches of 100
            for i in range(0, len(catalog_object_ids), 100):
                batch = catalog_object_ids[i:i+100]
                batch_num = (i // 100) + 1
                
                print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} items)...")
                
                request_body = {
                    "catalog_object_ids": batch
                }
                
                response = requests.post(
                    url, 
                    headers=self._get_headers(), 
                    json=request_body, 
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    counts = data.get('counts', [])
                    
                    print(f"✅ Batch {batch_num} successful: received data for {len(counts)} items")
                    
                    for count_data in counts:
                        catalog_object_id = count_data.get('catalog_object_id')
                        quantity = count_data.get('quantity', '0')

                        try:
                            # Convert to integer, default to 0 if parsing fails
                            inventory_counts[catalog_object_id] = int(float(quantity))
                        except (ValueError, TypeError):
                            print(f"⚠️  Invalid quantity '{quantity}' for {catalog_object_id[-8:] if len(catalog_object_id) > 8 else catalog_object_id}, using 0")
                            inventory_counts[catalog_object_id] = 0

                    # For items in this batch that didn't return inventory data,
                    # check if they have inventory tracking enabled
                    returned_ids = set(count_data.get('catalog_object_id') for count_data in counts)
                    missing_ids = set(batch) - returned_ids

                    if missing_ids:
                        print(f"🔍 Checking {len(missing_ids)} items without inventory data...")
                        catalog_info = self._get_catalog_objects(list(missing_ids))

                        for missing_id in missing_ids:
                            catalog_obj = catalog_info.get(missing_id)
                            if catalog_obj:
                                # Check if this variation has inventory tracking enabled
                                item_variation_data = catalog_obj.get('item_variation_data', {})
                                track_inventory = item_variation_data.get('track_inventory', False)

                                if track_inventory:
                                    # Item should have inventory but doesn't - assume 0
                                    inventory_counts[missing_id] = 0
                                    print(f"📦 {missing_id[-8:]}: Inventory tracking enabled but no data - setting to 0")
                                else:
                                    # Item doesn't track inventory - we'll skip updating it
                                    print(f"⚠️  {missing_id[-8:]}: Inventory tracking disabled - will skip update")
                            else:
                                # Couldn't get catalog info - assume 0 for safety
                                inventory_counts[missing_id] = 0
                                print(f"❓ {missing_id[-8:]}: No catalog info - setting to 0")
                
                elif response.status_code == 400:
                    print(f"❌ Batch {batch_num} failed: Bad request - {response.text}")
                    # Continue with other batches
                elif response.status_code == 401:
                    print(f"❌ Batch {batch_num} failed: Unauthorized - check your Square API credentials")
                    break  # Don't continue if auth fails
                else:
                    print(f"❌ Batch {batch_num} failed: {response.status_code} - {response.text}")
            
            print(f"📊 Retrieved inventory data for {len(inventory_counts)} out of {len(catalog_object_ids)} requested products")
            
            # Report products without inventory data
            missing_data = [id for id in catalog_object_ids if id not in inventory_counts]
            if missing_data:
                print(f"⚠️  No inventory data available for {len(missing_data)} products (likely inventory tracking disabled)")
                if len(missing_data) <= 5:
                    for missing_id in missing_data:
                        print(f"   • {missing_id[-8:] if len(missing_id) > 8 else missing_id}")
                else:
                    print(f"   • First 5: {', '.join([id[-8:] if len(id) > 8 else id for id in missing_data[:5]])}...")
            
            return inventory_counts

        except Exception as e:
            print(f"Error in batch retrieve inventory counts: {e}")
            return {}

    def _get_catalog_objects(self, catalog_object_ids):
        """
        Get catalog object details from Square API

        Args:
            catalog_object_ids (list): List of catalog object IDs

        Returns:
            dict: Mapping of catalog_object_id to catalog object data
        """
        try:
            if not catalog_object_ids:
                return {}

            url = f"{self.base_url}/v2/catalog/batch-retrieve"

            request_body = {
                "object_ids": catalog_object_ids
            }

            response = requests.post(
                url,
                headers=self._get_headers(),
                json=request_body,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                objects = data.get('objects', [])

                # Create mapping of ID to object
                catalog_map = {}
                for obj in objects:
                    catalog_map[obj.get('id')] = obj

                return catalog_map
            else:
                print(f"Error fetching catalog objects: {response.status_code}")
                return {}

        except Exception as e:
            print(f"Error in _get_catalog_objects: {e}")
            return {}

    def enable_inventory_tracking_for_all(self, products):
        """
        Enable inventory tracking for all provided products in Square

        Args:
            products (list): List of Product objects with square_variation_id

        Returns:
            int: Number of products successfully updated
        """
        try:
            if not products:
                return 0

            print(f"🔧 Enabling inventory tracking for {len(products)} products in Square...")

            enabled_count = 0

            # Process products in batches to avoid API limits
            for i in range(0, len(products), 10):
                batch = products[i:i+10]
                batch_num = (i // 10) + 1
                total_batches = (len(products) + 9) // 10

                print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} items)...")

                for product in batch:
                    if self._enable_inventory_tracking_for_product(product):
                        enabled_count += 1

                # Small delay between batches to avoid rate limiting
                import time
                time.sleep(0.5)

            print(f"✅ Enabled inventory tracking for {enabled_count} out of {len(products)} products")
            return enabled_count

        except Exception as e:
            print(f"Error enabling inventory tracking: {e}")
            return 0

    def _enable_inventory_tracking_for_product(self, product):
        """
        Enable inventory tracking for a single product variation in Square

        Args:
            product: Product object with square_variation_id and square_item_id

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not product.square_variation_id or not product.square_item_id:
                print(f"⚠️  Missing Square IDs for {product.name}")
                return False

            url = f"{self.base_url}/v2/catalog/object/{product.square_variation_id}"

            # First, get the current variation data
            response = requests.get(url, headers=self._get_headers(), timeout=30)

            if response.status_code != 200:
                print(f"❌ Failed to get variation data for {product.name}: {response.status_code}")
                return False

            variation_data = response.json().get('object', {})

            # Update the variation to enable inventory tracking
            variation_data['item_variation_data']['track_inventory'] = True

            # Set initial inventory count to current stock
            if 'inventory_alert_threshold' not in variation_data['item_variation_data']:
                variation_data['item_variation_data']['inventory_alert_threshold'] = product.reorder_level or 10

            # Update the variation
            update_url = f"{self.base_url}/v2/catalog/object/{product.square_variation_id}"
            update_body = {
                "object": variation_data
            }

            update_response = requests.put(
                update_url,
                headers=self._get_headers(),
                json=update_body,
                timeout=30
            )

            if update_response.status_code == 200:
                print(f"✅ Enabled inventory tracking for {product.name}")

                # Now set the initial inventory count
                self._set_inventory_count(product.square_variation_id, product.current_stock)
                return True
            else:
                print(f"❌ Failed to update {product.name}: {update_response.status_code}")
                return False

        except Exception as e:
            print(f"Error enabling tracking for {product.name}: {e}")
            return False

    def _set_inventory_count(self, variation_id, count):
        """
        Set inventory count for a variation in Square

        Args:
            variation_id (str): Square variation ID
            count (int): Inventory count to set
        """
        try:
            url = f"{self.base_url}/v2/inventory/batch-change"

            # Get location ID (use first available location)
            locations = self.get_locations()
            if not locations:
                print("⚠️  No locations found, cannot set inventory")
                return False

            location_id = locations[0].get('id')

            request_body = {
                "changes": [
                    {
                        "type": "PHYSICAL_COUNT",
                        "physical_count": {
                            "catalog_object_id": variation_id,
                            "location_id": location_id,
                            "quantity": str(count),
                            "occurred_at": datetime.now().isoformat() + "Z"
                        }
                    }
                ]
            }

            response = requests.post(
                url,
                headers=self._get_headers(),
                json=request_body,
                timeout=30
            )

            if response.status_code == 200:
                print(f"✅ Set inventory count to {count} for variation {variation_id[-8:]}")
                return True
            else:
                print(f"⚠️  Failed to set inventory count: {response.status_code}")
                return False

        except Exception as e:
            print(f"Error setting inventory count: {e}")
            return False

    def _attempt_product_mapping(self, unmapped_products):
        """
        Attempt to map products without Square variation IDs by matching names with Square catalog
        
        Args:
            unmapped_products (list): List of Product objects without square_variation_id
            
        Returns:
            int: Number of products successfully mapped
        """
        try:
            if not unmapped_products:
                return 0
            
            # Fetch the current Square catalog to get all variations
            print("📚 Fetching Square catalog for product mapping...")
            catalog_items = self._fetch_catalog_items()
            
            if not catalog_items:
                print("⚠️  No catalog items found in Square")
                return 0
            
            # Build a mapping of variation names to their IDs
            square_variations = {}
            for item in catalog_items:
                item_data = item.get('item_data', {})
                parent_name = item_data.get('name', '')
                variations = item_data.get('variations', [])
                
                for variation in variations:
                    var_data = variation.get('item_variation_data', {})
                    var_name = var_data.get('name', '')
                    var_id = variation.get('id', '')
                    
                    if var_name and var_id:
                        # Store both the variation name and parent name for matching
                        square_variations[var_name.lower().strip()] = {
                            'id': var_id,
                            'name': var_name,
                            'parent': parent_name,
                            'item_id': item.get('id', '')
                        }
                        
                        # Also try building a combined name like "Parent - Variation"
                        if parent_name and var_name != parent_name:
                            combined_name = f"{parent_name} - {var_name}".lower().strip()
                            square_variations[combined_name] = {
                                'id': var_id,
                                'name': var_name,
                                'parent': parent_name,
                                'item_id': item.get('id', '')
                            }
            
            print(f"📖 Found {len(square_variations)} Square variations for matching")
            
            mapped_count = 0
            
            # Try to match each unmapped product
            for product in unmapped_products:
                product_name_lower = product.name.lower().strip()
                
                # Try exact match first
                if product_name_lower in square_variations:
                    var_info = square_variations[product_name_lower]
                    product.square_variation_id = var_info['id']
                    product.square_item_id = var_info['item_id']
                    product.save()
                    print(f"✅ Mapped '{product.name}' to Square variation {var_info['id'][-8:]}")
                    mapped_count += 1
                    continue
                
                # Try partial matching for products with size/variant patterns
                best_match = None
                for square_name, var_info in square_variations.items():
                    # Check if the Square name contains key parts of our product name
                    product_parts = product_name_lower.split(' - ')
                    if len(product_parts) > 1:
                        # Extract key parts (like size, type, etc.)
                        for part in product_parts:
                            part = part.strip()
                            if len(part) > 2 and part in square_name:
                                best_match = var_info
                                break
                    
                    if best_match:
                        break
                
                if best_match:
                    product.square_variation_id = best_match['id']
                    product.square_item_id = best_match['item_id']
                    product.save()
                    print(f"🔗 Mapped '{product.name}' to '{best_match['name']}' (partial match)")
                    mapped_count += 1
            
            return mapped_count
            
        except Exception as e:
            print(f"Error attempting product mapping: {e}")
            return 0
    
    def _get_location_name(self, location_id, location_cache):
        if location_id not in location_cache:
            locations = self.get_locations()
            location_name = next(
                (loc['name'] for loc in locations if loc['id'] == location_id),
                "Market Stall"
            )
            location_cache[location_id] = location_name
        
        return location_cache[location_id]
