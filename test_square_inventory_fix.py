#!/usr/bin/env python3
"""
Test script to validate the Square inventory sync fix

This script demonstrates the improved inventory sync that now:
1. Maps unmapped products automatically
2. Syncs inventory for all mapped products
3. Provides detailed feedback about the process

Run this script after implementing the fix to see the improvements.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.square_integration import SquareIntegration
from app.models.database import get_db, init_db
from app.models.products import Product

def test_inventory_sync_fix():
    """Test the improved Square inventory sync"""
    
    print("🧪 Testing Square Inventory Sync Fix")
    print("=" * 50)
    
    # Initialize database
    print("📋 Initializing database...")
    init_db()
    
    # Create Square integration instance
    square = SquareIntegration()
    
    # Test 1: Check connection
    print("\n1️⃣  Testing Square Connection...")
    success, message = square.test_connection()
    print(f"   {message}")
    
    if not success:
        print("❌ Cannot proceed without Square connection")
        return False
    
    # Test 2: Check current product mapping state
    print("\n2️⃣  Analyzing Current Product Mapping...")
    products = Product.get_all()
    mapped_products = [p for p in products if p.square_variation_id]
    unmapped_products = [p for p in products if not p.square_variation_id]
    
    print(f"   📊 Total products: {len(products)}")
    print(f"   ✅ Mapped to Square: {len(mapped_products)}")
    print(f"   ❌ Unmapped: {len(unmapped_products)}")
    
    if unmapped_products:
        print(f"   📝 Sample unmapped products:")
        for product in unmapped_products[:5]:
            print(f"      • {product.name}")
        if len(unmapped_products) > 5:
            print(f"      ... and {len(unmapped_products) - 5} more")
    
    # Test 3: Test the improved inventory sync
    print("\n3️⃣  Testing Improved Inventory Sync...")
    print("   🔄 Running sync_inventory_counts() with new fixes...")
    
    try:
        updated_count = square.sync_inventory_counts()
        print(f"   ✅ Inventory sync completed! Updated {updated_count} products.")
        
        # Check mapping improvements
        products_after = Product.get_all()
        mapped_after = [p for p in products_after if p.square_variation_id]
        
        print(f"   📈 Products mapped after sync: {len(mapped_after)} (was {len(mapped_products)})")
        
        if len(mapped_after) > len(mapped_products):
            newly_mapped = len(mapped_after) - len(mapped_products)
            print(f"   🎉 Successfully mapped {newly_mapped} additional products!")
    
    except Exception as e:
        print(f"   ❌ Error during inventory sync: {e}")
        return False
    
    # Test 4: Test full sync with inventory included
    print("\n4️⃣  Testing Full Sync with Inventory...")
    print("   🔄 Running sync_all_data() with inventory sync included...")
    
    try:
        results = square.sync_all_data()
        print(f"   📋 Sync Results:")
        print(f"      • Products synced: {results.get('products_synced', 0)}")
        print(f"      • Inventory updated: {results.get('inventory_synced', 0)}")
        print(f"      • Orders synced: {results.get('orders_synced', 0)}")
        print(f"      • Locations synced: {results.get('locations_synced', 0)}")
        
        if results.get('errors'):
            print(f"      ⚠️  Errors: {len(results['errors'])}")
            for error in results['errors'][:3]:  # Show first 3 errors
                print(f"         - {error}")
        
        print(f"   💬 Message: {results.get('message', 'No message')}")
        
    except Exception as e:
        print(f"   ❌ Error during full sync: {e}")
        return False
    
    # Test 5: Show final state
    print("\n5️⃣  Final State Analysis...")
    final_products = Product.get_all()
    final_mapped = [p for p in final_products if p.square_variation_id]
    final_unmapped = [p for p in final_products if not p.square_variation_id]
    
    print(f"   📊 Final Results:")
    print(f"      • Total products: {len(final_products)}")
    print(f"      • Mapped to Square: {len(final_mapped)}")
    print(f"      • Still unmapped: {len(final_unmapped)}")
    
    if final_unmapped:
        print(f"   📝 Products still unmapped (may need manual mapping):")
        for product in final_unmapped[:5]:
            print(f"      • {product.name}")
        if len(final_unmapped) > 5:
            print(f"      ... and {len(final_unmapped) - 5} more")
    
    print("\n" + "=" * 50)
    print("✅ Inventory Sync Fix Test Complete!")
    
    # Summary of improvements
    improvement_summary = f"""
🎯 FIX SUMMARY:
• Before: Only {len(mapped_products)} products could sync inventory
• After: {len(final_mapped)} products can sync inventory
• Improvement: {len(final_mapped) - len(mapped_products)} more products now mapped
• Automatic mapping: Products are now mapped during inventory sync
• Better feedback: Clear reporting of mapped/unmapped products
• Resilient sync: Continues even if some products can't be mapped
"""
    
    print(improvement_summary)
    return True

def show_usage_instructions():
    """Show how to use the improved inventory sync"""
    
    instructions = """
📖 HOW TO USE THE IMPROVED INVENTORY SYNC:

1. Via Frontend (Inventory Page):
   • Click "Sync Stock Levels" button
   • Will now map unmapped products automatically
   • Shows detailed progress and results

2. Via Main Sync (Dashboard):
   • Click "Sync Square Data" button  
   • Now includes inventory sync automatically
   • Happens after catalog sync for best results

3. Via API/Code:
   ```python
   from app.services.square_integration import SquareIntegration
   
   square = SquareIntegration()
   
   # Option 1: Just inventory sync (with auto-mapping)
   updated_count = square.sync_inventory_counts()
   
   # Option 2: Full sync (includes inventory)
   results = square.sync_all_data()
   ```

4. Troubleshooting:
   • Use "Debug" button on inventory page to see mapping details
   • Use "Fix Mapping" button to force remapping
   • Check console logs for detailed progress information

🔧 KEY IMPROVEMENTS:
• No longer skips products without Square IDs
• Automatically attempts to map unmapped products
• Provides detailed feedback about what's happening
• Continues processing even if some products fail
• Includes inventory sync in main sync process
"""
    
    print(instructions)

if __name__ == "__main__":
    print("🚀 Square Inventory Sync Fix Validator")
    print("This script tests the improvements made to Square inventory sync")
    print()
    
    try:
        success = test_inventory_sync_fix()
        
        if success:
            print("\n" + "🎉 All tests passed! The fix is working correctly.")
            show_usage_instructions()
        else:
            print("\n" + "❌ Some tests failed. Check the error messages above.")
            
    except Exception as e:
        print(f"\n❌ Test script failed with error: {e}")
        print("Please check your Square API configuration and try again.")
