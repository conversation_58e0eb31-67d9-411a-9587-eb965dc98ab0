"""
Configuration settings for Market Stall Business Tracker
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Base configuration class"""
    
    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    FLASK_PORT = int(os.getenv('FLASK_PORT', 5000))
    
    # Database settings
    DATABASE_PATH = os.getenv('DATABASE_PATH', 'market_stall.db')
    
    # Square API settings
    SQUARE_APPLICATION_ID = os.getenv('SQUARE_APPLICATION_ID')
    SQUARE_ACCESS_TOKEN = os.getenv('SQUARE_ACCESS_TOKEN')
    SQUARE_ENVIRONMENT = os.getenv('SQUARE_ENVIRONMENT', 'sandbox')
    
    # Cache settings
    CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', 300))  # 5 minutes default
    
    # Business settings
    DEFAULT_LABOR_RATE = float(os.getenv('DEFAULT_LABOR_RATE', 15.0))
    DEFAULT_CONSIGNMENT_SPLIT = float(os.getenv('DEFAULT_CONSIGNMENT_SPLIT', 0.5))

class DevelopmentConfig(Config):
    """Development configuration"""
    FLASK_DEBUG = True
    DATABASE_PATH = 'market_stall_dev.db'

class ProductionConfig(Config):
    """Production configuration"""
    FLASK_DEBUG = False
    SECRET_KEY = os.getenv('SECRET_KEY', 'production-secret-key-change-me')  # Must be set in production

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_PATH = ':memory:'  # In-memory database for tests

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
