/* Market Stall Business Tracker - Main Styles */

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
}

/* Sidebar icons */
.sidebar-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

/* Navigation active state */
.nav-item.active {
    background-color: #eff6ff;
    color: #2563eb;
}

/* Fade in animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Loading spinner */
.loading-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #2563eb;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-badge.warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.status-badge.error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.status-badge.info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

/* Metric cards */
.metric-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.metric-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.metric-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.metric-change {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.metric-change.positive {
    color: #059669;
}

.metric-change.negative {
    color: #dc2626;
}

/* Table styles */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    text-align: left;
    padding: 0.75rem 1rem;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.data-table tr:hover {
    background-color: #f9fafb;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input.error {
    border-color: #dc2626;
}

.form-input.error:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #2563eb;
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-success {
    background-color: #059669;
    color: white;
}

.btn-success:hover {
    background-color: #047857;
}

.btn-warning {
    background-color: #d97706;
    color: white;
}

.btn-warning:hover {
    background-color: #b45309;
}

.btn-danger {
    background-color: #dc2626;
    color: white;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.modal-content {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}

/* Product variant styles */
.expand-icon {
    transition: transform 0.2s ease-in-out;
}

.variant-row {
    background-color: #f9fafb !important;
    border-left: 3px solid #e5e7eb;
}

.variant-row:hover {
    background-color: #f3f4f6 !important;
}

.variant-container {
    transition: all 0.2s ease-in-out;
}

.variant-container.hidden {
    display: none;
}

/* Product table improvements */
.product-table-row {
    transition: background-color 0.15s ease-in-out;
}

.product-table-row:hover {
    background-color: #f9fafb;
}

.product-table-row.variant-row:hover {
    background-color: #f3f4f6 !important;
}

/* Stock status indicators */
.stock-indicator.out {
    color: #dc2626;
    font-weight: 500;
}

.stock-indicator.low {
    color: #d97706;
    font-weight: 500;
}

.stock-indicator.good {
    color: #059669;
    font-weight: 500;
}

.out-of-stock {
    background-color: #fef2f2;
}

.low-stock {
    background-color: #fffbeb;
}

/* Profit margin indicators */
.profit-margin {
    font-weight: 500;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.profit-margin.high {
    background-color: #dcfce7;
    color: #166534;
}

.profit-margin.medium {
    background-color: #fef3c7;
    color: #92400e;
}

.profit-margin.low {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Print styles */
@media print {
    .sidebar,
    .nav-item,
    button,
    .btn {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .chart-container {
        height: auto !important;
    }
}
