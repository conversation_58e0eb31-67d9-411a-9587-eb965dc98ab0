/* Component-specific styles for Market Stall Business Tracker */

/* COGS Modal specific styles */
.cogs-modal {
    max-width: 42rem;
    width: 100%;
}

.cogs-section {
    border-left: 3px solid transparent;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

.cogs-section.materials {
    border-left-color: #10b981;
}

.cogs-section.assembly {
    border-left-color: #3b82f6;
}

.cogs-section.labor {
    border-left-color: #8b5cf6;
}

.cogs-section.other {
    border-left-color: #f59e0b;
}

.cogs-section.consignment {
    border-left-color: #eab308;
}

/* Step indicators */
.step-indicator {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.5rem;
}

.step-indicator.materials {
    background-color: #d1fae5;
    color: #065f46;
}

.step-indicator.assembly {
    background-color: #dbeafe;
    color: #1e40af;
}

.step-indicator.labor {
    background-color: #e9d5ff;
    color: #6b21a8;
}

.step-indicator.other {
    background-color: #fed7aa;
    color: #9a3412;
}

.step-indicator.consignment {
    background-color: #fef3c7;
    color: #92400e;
}

/* Dashboard specific components */
.dashboard-banner {
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.dashboard-banner.square {
    background-color: #fefce8;
    border: 1px solid #fde047;
}

.dashboard-banner.cogs {
    background-color: #eff6ff;
    border: 1px solid #93c5fd;
}

/* Chart legend customization */
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 0.5rem;
}

/* Product table specific styles */
.product-table-row {
    transition: background-color 0.15s ease-in-out;
}

.product-table-row:hover {
    background-color: #f8fafc;
}

.product-table-row.low-stock {
    background-color: #fef2f2;
}

.product-table-row.out-of-stock {
    background-color: #fee2e2;
}

/* Stock level indicators */
.stock-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.stock-indicator.good {
    background-color: #d1fae5;
    color: #065f46;
}

.stock-indicator.low {
    background-color: #fef3c7;
    color: #92400e;
}

.stock-indicator.out {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Profit margin indicators */
.profit-margin {
    font-weight: 600;
}

.profit-margin.excellent {
    color: #059669;
}

.profit-margin.good {
    color: #10b981;
}

.profit-margin.fair {
    color: #d97706;
}

.profit-margin.poor {
    color: #dc2626;
}

.profit-margin.negative {
    color: #991b1b;
}

/* Sales form specific styles */
.sales-form {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.profit-preview {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
}

.profit-preview-item {
    text-align: center;
}

.profit-preview-label {
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.profit-preview-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

/* Analytics specific components */
.analytics-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.analytics-metric {
    text-align: center;
}

.analytics-metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.analytics-metric-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.analytics-metric-change {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Settings form styles */
.settings-section {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.settings-section.danger {
    border-color: #fecaca;
    background-color: #fef2f2;
}

.settings-section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.settings-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.settings-section-description {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Connection status indicators */
.connection-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.connection-status.connected {
    background-color: #d1fae5;
    color: #065f46;
}

.connection-status.disconnected {
    background-color: #fee2e2;
    color: #991b1b;
}

.connection-status.testing {
    background-color: #fef3c7;
    color: #92400e;
}

/* Inventory alerts */
.inventory-alert {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.inventory-alert-icon {
    color: #dc2626;
    width: 1.25rem;
    height: 1.25rem;
}

.inventory-alert-content {
    margin-left: 0.75rem;
}

.inventory-alert-title {
    font-weight: 600;
    color: #991b1b;
    margin-bottom: 0.25rem;
}

.inventory-alert-message {
    font-size: 0.875rem;
    color: #7f1d1d;
}

/* Responsive adjustments for components */
@media (max-width: 768px) {
    .cogs-modal {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
    
    .analytics-metric-value {
        font-size: 2rem;
    }
    
    .profit-preview {
        padding: 0.75rem;
    }
    
    .settings-section {
        padding: 1rem;
    }
}
