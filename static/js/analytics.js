/**
 * Analytics functionality for Market Stall Business Tracker
 */

// Chart instances
let analyticsProfitChart = null;
let productProfitabilityChart = null;

/**
 * Load analytics page
 */
async function loadAnalyticsPage() {
    try {
        setDefaultDateRange();
        await updateAnalytics();
    } catch (error) {
        console.error('Error loading analytics page:', error);
        showWarning('Failed to load analytics data');
    }
}

/**
 * Set default date range
 */
function setDefaultDateRange() {
    const dateFrom = document.getElementById('analytics-date-from');
    const dateTo = document.getElementById('analytics-date-to');
    
    if (dateFrom && dateTo) {
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        dateFrom.value = thirtyDaysAgo.toISOString().split('T')[0];
        dateTo.value = today.toISOString().split('T')[0];
    }
}

/**
 * Update analytics
 */
async function updateAnalytics() {
    try {
        const dateFrom = document.getElementById('analytics-date-from')?.value;
        const dateTo = document.getElementById('analytics-date-to')?.value;
        
        showLoading('analytics-summary');
        
        const response = await apiRequest(`/analytics/api/analytics/profit-analysis?date_from=${dateFrom}&date_to=${dateTo}`);
        
        if (response.success) {
            updateAnalyticsSummary(response.data.summary.summary);
            updateAnalyticsCharts(response.data);
            await loadProductProfitability(dateFrom, dateTo);
            await loadLocationPerformance(dateFrom, dateTo);
            await loadConsignmentReport(dateFrom, dateTo);
        } else {
            showError('analytics-summary', 'Failed to load analytics data');
        }
    } catch (error) {
        console.error('Error updating analytics:', error);
        showError('analytics-summary', 'Failed to load analytics data');
    }
}

/**
 * Update analytics summary
 */
function updateAnalyticsSummary(summary) {
    const container = document.getElementById('analytics-summary');
    if (!container) return;
    
    const metrics = [
        {
            label: 'Total Revenue',
            value: formatCurrency(summary.total_revenue || 0),
            icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
            color: 'blue'
        },
        {
            label: 'Gross Profit',
            value: formatCurrency(summary.total_profit || 0),
            icon: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6',
            color: 'green'
        },
        {
            label: 'Avg Profit Margin',
            value: formatPercentage(summary.avg_profit_margin || 0),
            icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
            color: 'purple'
        },
        {
            label: 'Total Sales',
            value: formatNumber(summary.total_sales || 0),
            icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
            color: 'indigo'
        }
    ];
    
    container.innerHTML = metrics.map(metric => `
        <div class="analytics-card">
            <div class="flex items-center justify-between">
                <div>
                    <div class="analytics-metric-value text-${metric.color}-600">${metric.value}</div>
                    <div class="analytics-metric-label">${metric.label}</div>
                </div>
                <div class="w-12 h-12 bg-${metric.color}-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-${metric.color}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${metric.icon}"></path>
                    </svg>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Update analytics charts
 */
function updateAnalyticsCharts(data) {
    // Update profit trends chart
    if (analyticsProfitChart) {
        analyticsProfitChart.destroy();
    }
    
    const profitChartData = {
        labels: data.trends.map(item => item.period),
        revenue: data.trends.map(item => item.revenue || 0),
        profit: data.trends.map(item => item.profit || 0)
    };
    
    analyticsProfitChart = createProfitTrendChart('analytics-profit-chart', profitChartData);
}

/**
 * Load product profitability
 */
async function loadProductProfitability(dateFrom, dateTo) {
    try {
        const response = await apiRequest(`/analytics/api/analytics/product-profitability?date_from=${dateFrom}&date_to=${dateTo}`);
        
        if (response.success) {
            updateProductProfitabilityChart(response.data);
            updateProductTable(response.data);
        }
    } catch (error) {
        console.error('Error loading product profitability:', error);
    }
}

/**
 * Update product profitability chart
 */
function updateProductProfitabilityChart(products) {
    if (productProfitabilityChart) {
        productProfitabilityChart.destroy();
    }
    
    const topProducts = products.slice(0, 10);
    const chartData = {
        labels: topProducts.map(p => p.product_name),
        values: topProducts.map(p => p.avg_profit_margin || 0)
    };
    
    productProfitabilityChart = createProfitMarginChart('product-profitability-chart', chartData);
}

/**
 * Update product table
 */
function updateProductTable(products) {
    const container = document.getElementById('analytics-product-table');
    if (!container) return;
    
    if (products.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">No product data available</p>';
        return;
    }
    
    const tableHtml = `
        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Sales</th>
                        <th>Revenue</th>
                        <th>Profit</th>
                        <th>Margin</th>
                    </tr>
                </thead>
                <tbody>
                    ${products.slice(0, 15).map(product => `
                        <tr>
                            <td>
                                <div class="font-medium">${product.product_name}</div>
                                <div class="text-sm text-gray-500">${product.category || 'No category'}</div>
                            </td>
                            <td>${formatNumber(product.sales_count)}</td>
                            <td>${formatCurrency(product.total_revenue)}</td>
                            <td class="${(product.total_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${formatCurrency(product.total_profit)}
                            </td>
                            <td class="profit-margin ${getProfitMarginClass(product.avg_profit_margin)}">
                                ${formatPercentage(product.avg_profit_margin)}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}

/**
 * Load location performance
 */
async function loadLocationPerformance(dateFrom, dateTo) {
    try {
        const response = await apiRequest(`/api/dashboard/location-performance?date_from=${dateFrom}&date_to=${dateTo}`);
        
        if (response.success) {
            updateLocationTable(response.data);
        }
    } catch (error) {
        console.error('Error loading location performance:', error);
    }
}

/**
 * Update location table
 */
function updateLocationTable(locations) {
    const container = document.getElementById('analytics-location-table');
    if (!container) return;
    
    if (locations.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">No location data available</p>';
        return;
    }
    
    const tableHtml = `
        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Location</th>
                        <th>Sales</th>
                        <th>Revenue</th>
                        <th>Profit</th>
                        <th>Margin</th>
                    </tr>
                </thead>
                <tbody>
                    ${locations.map(location => `
                        <tr>
                            <td class="font-medium">${location.location}</td>
                            <td>${formatNumber(location.sales_count)}</td>
                            <td>${formatCurrency(location.total_revenue)}</td>
                            <td class="${(location.total_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${formatCurrency(location.total_profit)}
                            </td>
                            <td class="profit-margin ${getProfitMarginClass(location.avg_profit_margin)}">
                                ${formatPercentage(location.avg_profit_margin)}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}

/**
 * Load consignment report
 */
async function loadConsignmentReport(dateFrom, dateTo) {
    try {
        const response = await apiRequest(`/analytics/api/analytics/consignment-report?date_from=${dateFrom}&date_to=${dateTo}`);
        
        if (response.success) {
            updateConsignmentReport(response.data);
        }
    } catch (error) {
        console.error('Error loading consignment report:', error);
    }
}

/**
 * Update consignment report
 */
function updateConsignmentReport(consignmentData) {
    const container = document.getElementById('consignment-report');
    if (!container) return;
    
    if (consignmentData.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">No consignment data available</p>';
        return;
    }
    
    const tableHtml = `
        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Partner</th>
                        <th>Sales</th>
                        <th>Revenue</th>
                        <th>Partner Share</th>
                        <th>Our Share</th>
                        <th>Net Profit</th>
                    </tr>
                </thead>
                <tbody>
                    ${consignmentData.map(item => `
                        <tr>
                            <td class="font-medium">${item.product_name}</td>
                            <td>${item.consignment_partner}</td>
                            <td>${formatNumber(item.sales_count)}</td>
                            <td>${formatCurrency(item.total_revenue)}</td>
                            <td class="text-red-600">${formatCurrency(item.partner_share)}</td>
                            <td class="text-blue-600">${formatCurrency(item.our_share)}</td>
                            <td class="${(item.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${formatCurrency(item.net_profit)}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}
