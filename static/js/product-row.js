/**
 * Render individual product row with proper variant logic
 */
function renderProductRow(product, isParent = true, hasVariants = false, variantCount = 0, parentId = null) {
    const stockStatus = getStockStatus(product);
    const profitMarginClass = getProfitMarginClass(product.avg_profit_margin);
    const isVariant = !isParent;
    
    return `
        <tr class="product-table-row ${stockStatus.class} ${isVariant ? 'variant-row bg-gray-50' : ''}" ${isParent ? `data-product-id="${product.id}"` : ''}>
            <td>
                <div class="flex items-center ${isVariant ? 'pl-8' : ''}">
                    ${isParent && hasVariants ? `
                        <button onclick="toggleVariants(${product.id})" 
                                class="mr-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                                title="Show/hide variants">
                            <svg class="w-4 h-4 expand-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    ` : ''}
                    ${!isParent && !hasVariants ? '<div class="w-6 mr-2"></div>' : ''}
                    <div>
                        <div class="font-medium ${isVariant ? 'text-sm text-gray-700' : ''}">
                            ${isVariant ? product.name.split(' - ').slice(1).join(' - ') : product.name}
                        </div>
                        ${product.is_consignment ? '<div class="text-xs text-purple-600">Consignment</div>' : ''}
                        ${isParent && hasVariants ? `<div class="text-xs text-blue-600">${variantCount} variants</div>` : ''}
                    </div>
                </div>
            </td>
            <td>
                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                    ${product.category || 'No category'}
                </span>
            </td>
            <td>
                <div class="flex items-center">
                    <span class="mr-2">${product.current_stock}</span>
                    <span class="stock-indicator ${stockStatus.indicator}">
                        ${stockStatus.text}
                    </span>
                </div>
                <div class="text-xs text-gray-500">Reorder: ${product.reorder_level}</div>
            </td>
            <td>
                <div class="font-medium">${formatCurrency(product.total_cogs)}</div>
                ${product.total_cogs > 0 ? 
                    `<div class="text-xs text-gray-500">
                        Materials: ${formatCurrency(product.raw_materials_cost + product.assembly_parts_cost)}<br>
                        Labor: ${formatCurrency((product.labor_hours || 0) * (product.labor_rate || 0))}
                    </div>` : 
                    `<div class="text-xs text-gray-500">
                        ${isParent && hasVariants ? 'Set on variants' : 'Not set'}
                    </div>`
                }
            </td>
            <td>
                ${product.avg_profit_margin ? 
                    `<span class="profit-margin ${profitMarginClass}">${formatPercentage(product.avg_profit_margin)}</span>` : 
                    '<span class="text-gray-400">-</span>'
                }
            </td>
            <td>
                <span class="px-2 py-1 text-xs rounded-full ${product.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${product.active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td>
                <div class="flex space-x-2">
                    ${isParent && hasVariants ? `
                        <button onclick="manageVariantCOGS(${product.id})" 
                                class="text-blue-600 hover:text-blue-900 text-sm">
                            Manage Variants
                        </button>
                    ` : `
                        <button onclick="openCOGSModal(${product.id}, '${product.name.replace(/'/g, "\\'")}', '${product.category || ''}')" 
                                class="text-blue-600 hover:text-blue-900 text-sm">
                            ${product.total_cogs > 0 ? 'Edit COGS' : 'Set COGS'}
                        </button>
                    `}
                    <button onclick="editProduct(${product.id})" 
                            class="text-green-600 hover:text-green-900 text-sm">
                        Edit
                    </button>
                    <button onclick="deleteProduct(${product.id}, '${product.name.replace(/'/g, "\\'")})" 
                            class="text-red-600 hover:text-red-900 text-sm">
                        Delete
                    </button>
                </div>
            </td>
        </tr>
    `;
}
