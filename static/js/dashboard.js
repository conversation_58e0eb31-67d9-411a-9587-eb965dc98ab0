/**
 * Dashboard functionality for Market Stall Business Tracker
 */

// Chart instances
let profitChart = null;
let locationChart = null;

/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        showLoading('metrics-grid');
        
        const timeFilter = document.getElementById('time-filter')?.value || '30';
        const dateRange = getDateRange(timeFilter === '7' ? 'week' : timeFilter === '30' ? 'month' : 'quarter');
        
        const response = await apiRequest(`/api/dashboard/summary?date_from=${dateRange.from}&date_to=${dateRange.to}`);
        
        if (response.success) {
            updateMetricsGrid(response.data.summary);
            updateProfitChart(response.data);
            updateLocationChart(response.data.location_performance);
            updateProductPerformanceTable(response.data.product_performance);
            updateInventoryAlerts(response.data.inventory_alerts);
            updateBanners(response.data);
        } else {
            showError('metrics-grid', response.error || 'Failed to load dashboard data');
        }
        
        updateLastUpdated();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showError('metrics-grid', 'Failed to load dashboard data');
    }
}

/**
 * Update metrics grid
 */
function updateMetricsGrid(summary) {
    const metricsGrid = document.getElementById('metrics-grid');
    if (!metricsGrid) return;
    
    const metrics = [
        {
            label: 'Total Revenue',
            value: formatCurrency(summary.total_revenue || 0),
            icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
            color: 'blue'
        },
        {
            label: 'Gross Profit',
            value: formatCurrency(summary.total_profit || 0),
            icon: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6',
            color: 'green'
        },
        {
            label: 'Profit Margin',
            value: formatPercentage(summary.avg_profit_margin || 0),
            icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
            color: 'purple'
        },
        {
            label: 'Total Sales',
            value: formatNumber(summary.total_sales || 0),
            icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
            color: 'indigo'
        }
    ];
    
    metricsGrid.innerHTML = metrics.map(metric => `
        <div class="metric-card">
            <div class="flex items-center justify-between">
                <div>
                    <div class="metric-value text-${metric.color}-600">${metric.value}</div>
                    <div class="metric-label">${metric.label}</div>
                </div>
                <div class="w-12 h-12 bg-${metric.color}-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-${metric.color}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${metric.icon}"></path>
                    </svg>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Update profit chart
 */
async function updateProfitChart(data) {
    try {
        const period = document.getElementById('profit-chart-period')?.value || 'day';
        const timeFilter = document.getElementById('time-filter')?.value || '30';
        const dateRange = getDateRange(timeFilter === '7' ? 'week' : timeFilter === '30' ? 'month' : 'quarter');
        
        const response = await apiRequest(`/api/dashboard/profit-trends?date_from=${dateRange.from}&date_to=${dateRange.to}&group_by=${period}`);
        
        if (response.success) {
            const ctx = document.getElementById('profit-chart');
            if (!ctx) return;
            
            // Destroy existing chart
            if (profitChart) {
                profitChart.destroy();
            }
            
            profitChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: response.data.map(item => item.period),
                    datasets: [
                        {
                            label: 'Revenue',
                            data: response.data.map(item => item.revenue || 0),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Profit',
                            data: response.data.map(item => item.profit || 0),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                                }
                            }
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error updating profit chart:', error);
    }
}

/**
 * Update location chart
 */
function updateLocationChart(locationData) {
    const ctx = document.getElementById('location-chart');
    if (!ctx || !locationData) return;
    
    // Destroy existing chart
    if (locationChart) {
        locationChart.destroy();
    }
    
    locationChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: locationData.map(item => item.location),
            datasets: [{
                data: locationData.map(item => item.total_profit || 0),
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6',
                    '#06b6d4'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${formatCurrency(context.parsed)}`;
                        }
                    }
                }
            }
        }
    });
}

/**
 * Update product performance table
 */
function updateProductPerformanceTable(productData) {
    const container = document.getElementById('product-performance-table');
    if (!container || !productData) return;
    
    if (productData.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">No product data available</p>';
        return;
    }
    
    const tableHtml = `
        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Sales</th>
                        <th>Revenue</th>
                        <th>Profit</th>
                        <th>Margin</th>
                    </tr>
                </thead>
                <tbody>
                    ${productData.slice(0, 10).map(product => `
                        <tr>
                            <td>
                                <div class="font-medium">${product.product_name}</div>
                                <div class="text-sm text-gray-500">${product.category || 'No category'}</div>
                            </td>
                            <td>${formatNumber(product.sales_count)}</td>
                            <td>${formatCurrency(product.total_revenue)}</td>
                            <td class="${(product.total_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">${formatCurrency(product.total_profit)}</td>
                            <td class="profit-margin ${getProfitMarginClass(product.avg_profit_margin)}">${formatPercentage(product.avg_profit_margin)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}

/**
 * Update inventory alerts
 */
function updateInventoryAlerts(alertsData) {
    const container = document.getElementById('inventory-alerts');
    if (!container || !alertsData) return;
    
    if (alertsData.low_stock_count === 0) {
        container.innerHTML = '<p class="text-green-600 text-sm">✅ All products have adequate stock</p>';
        return;
    }
    
    container.innerHTML = `
        <div class="space-y-2">
            <p class="text-red-600 font-medium">${alertsData.low_stock_count} products need restocking</p>
            ${alertsData.low_stock_products.slice(0, 3).map(product => `
                <div class="text-sm text-gray-600">
                    • ${product.name}: ${product.current_stock} left
                </div>
            `).join('')}
            ${alertsData.low_stock_count > 3 ? `<div class="text-sm text-gray-500">...and ${alertsData.low_stock_count - 3} more</div>` : ''}
        </div>
    `;
}

/**
 * Update banners based on data
 */
function updateBanners(data) {
    const squareBanner = document.getElementById('square-banner');
    const cogsBanner = document.getElementById('cogs-status-banner');
    
    // Show/hide banners based on data availability
    const hasSquareData = data.summary && data.summary.total_sales > 0;
    const hasProfit = data.summary && data.summary.total_profit > 0;
    
    if (squareBanner) {
        if (!hasSquareData) {
            squareBanner.classList.remove('hidden');
        } else {
            squareBanner.classList.add('hidden');
        }
    }
    
    if (cogsBanner) {
        if (hasSquareData && !hasProfit) {
            cogsBanner.classList.remove('hidden');
        } else {
            cogsBanner.classList.add('hidden');
        }
    }
}

/**
 * Get profit margin CSS class
 */
function getProfitMarginClass(margin) {
    if (!margin) return '';
    
    if (margin >= 30) return 'excellent';
    if (margin >= 20) return 'good';
    if (margin >= 10) return 'fair';
    if (margin >= 0) return 'poor';
    return 'negative';
}

/**
 * Refresh dashboard
 */
function refreshDashboard() {
    loadDashboardData();
    showSuccess('Dashboard refreshed');
}

/**
 * Sync Square data
 */
async function syncSquareData() {
    try {
        showWarning('Syncing Square data...');
        
        const response = await apiRequest('/sales/api/sales/square-sync', {
            method: 'POST'
        });
        
        if (response.success) {
            const message = response.message || 
                `Synced ${response.products_synced} products, ${response.orders_synced} orders`;
            
            showSuccess(message);
            
            // Show errors if any
            if (response.errors && response.errors.length > 0) {
                setTimeout(() => {
                    showWarning(`${response.errors.length} warnings during sync`);
                }, 2000);
            }
            
            loadDashboardData(); // Refresh dashboard
        } else {
            showWarning(response.error || response.message || 'Failed to sync Square data');
        }
    } catch (error) {
        console.error('Error syncing Square data:', error);
        showWarning('Failed to sync Square data');
    }
}

/**
 * COGS Modal Functions - Preserved exactly as-is from original
 */
function openCOGSModal(productId, productName, productCategory) {
    document.getElementById('cogs-product-id').value = productId;
    document.getElementById('cogs-product-name').textContent = productName;
    document.getElementById('cogs-product-category').textContent = productCategory || 'No category';

    // Load existing COGS data if available
    loadExistingCOGSData(productId);

    document.getElementById('cogs-modal').classList.remove('hidden');
}

function closeCOGSModal() {
    document.getElementById('cogs-modal').classList.add('hidden');
    document.getElementById('cogs-form').reset();
}

async function loadExistingCOGSData(productId) {
    try {
        const response = await apiRequest('/inventory/api/products');
        if (response.success) {
            const product = response.data.find(p => p.id === productId);

            if (product && product.total_cogs > 0) {
                document.getElementById('raw-materials-cost').value = product.raw_materials_cost || 0;
                document.getElementById('assembly-parts-cost').value = product.assembly_parts_cost || 0;
                document.getElementById('labor-hours').value = product.labor_hours || 0;
                document.getElementById('labor-rate').value = product.labor_rate || 15;
                document.getElementById('packaging-cost').value = product.packaging_cost || 0;
                document.getElementById('overhead-cost').value = product.overhead_cost || 0;
                document.getElementById('is-consignment').checked = product.is_consignment || false;
                document.getElementById('consignment-partner').value = product.consignment_partner || '';
                document.getElementById('consignment-split').value = product.consignment_split || 0.5;

                // Show/hide consignment details
                toggleConsignmentDetails();

                // Update calculations
                updateCOGSCalculations();
            }
        }
    } catch (error) {
        console.error('Error loading COGS data:', error);
    }
}

function updateCOGSCalculations() {
    const rawMaterials = parseFloat(document.getElementById('raw-materials-cost').value) || 0;
    const assemblyParts = parseFloat(document.getElementById('assembly-parts-cost').value) || 0;
    const laborHours = parseFloat(document.getElementById('labor-hours').value) || 0;
    const laborRate = parseFloat(document.getElementById('labor-rate').value) || 15;
    const packaging = parseFloat(document.getElementById('packaging-cost').value) || 0;
    const overhead = parseFloat(document.getElementById('overhead-cost').value) || 0;

    const laborCost = laborHours * laborRate;
    const totalCOGS = rawMaterials + assemblyParts + laborCost + packaging + overhead;
    const otherCosts = packaging + overhead;

    // Update summary display
    document.getElementById('total-cogs').textContent = formatCurrency(totalCOGS);
    document.getElementById('summary-materials').textContent = formatCurrency(rawMaterials);
    document.getElementById('summary-assembly').textContent = formatCurrency(assemblyParts);
    document.getElementById('summary-labor').textContent = formatCurrency(laborCost);
    document.getElementById('summary-other').textContent = formatCurrency(otherCosts);
}

function toggleConsignmentDetails() {
    const isConsignment = document.getElementById('is-consignment').checked;
    const details = document.getElementById('consignment-details');
    if (isConsignment) {
        details.classList.remove('hidden');
    } else {
        details.classList.add('hidden');
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Time filter change
    const timeFilter = document.getElementById('time-filter');
    if (timeFilter) {
        timeFilter.addEventListener('change', loadDashboardData);
    }

    // Profit chart period change
    const profitChartPeriod = document.getElementById('profit-chart-period');
    if (profitChartPeriod) {
        profitChartPeriod.addEventListener('change', () => updateProfitChart({}));
    }

    // COGS calculation inputs
    ['raw-materials-cost', 'assembly-parts-cost', 'labor-hours', 'labor-rate', 'packaging-cost', 'overhead-cost'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateCOGSCalculations);
        }
    });

    // Consignment toggle
    const consignmentToggle = document.getElementById('is-consignment');
    if (consignmentToggle) {
        consignmentToggle.addEventListener('change', toggleConsignmentDetails);
    }

    // COGS form submission
    const cogsForm = document.getElementById('cogs-form');
    if (cogsForm) {
        cogsForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const productId = document.getElementById('cogs-product-id').value;
            const formData = {
                raw_materials_cost: parseFloat(document.getElementById('raw-materials-cost').value) || 0,
                assembly_parts_cost: parseFloat(document.getElementById('assembly-parts-cost').value) || 0,
                labor_hours: parseFloat(document.getElementById('labor-hours').value) || 0,
                labor_rate: parseFloat(document.getElementById('labor-rate').value) || 15,
                packaging_cost: parseFloat(document.getElementById('packaging-cost').value) || 0,
                overhead_cost: parseFloat(document.getElementById('overhead-cost').value) || 0,
                is_consignment: document.getElementById('is-consignment').checked,
                consignment_partner: document.getElementById('consignment-partner').value || '',
                consignment_split: parseFloat(document.getElementById('consignment-split').value) || 0.5
            };

            try {
                const response = await apiRequest(`/inventory/api/products/${productId}`, {
                    method: 'PUT',
                    body: JSON.stringify(formData)
                });

                if (response.success) {
                    const totalCOGS = formData.raw_materials_cost + formData.assembly_parts_cost +
                                    (formData.labor_hours * formData.labor_rate) +
                                    formData.packaging_cost + formData.overhead_cost;

                    showSuccess(`COGS saved successfully! Total COGS: ${formatCurrency(totalCOGS)}`);
                    closeCOGSModal();

                    // Refresh current view
                    if (currentView === 'inventory') {
                        loadInventoryPage();
                    } else if (currentView === 'dashboard') {
                        loadDashboardData();
                    }
                } else {
                    showWarning('Error saving COGS: ' + (response.error || 'Unknown error'));
                }
            } catch (error) {
                showWarning('Error saving COGS: ' + error.message);
            }
        });
    }
});
