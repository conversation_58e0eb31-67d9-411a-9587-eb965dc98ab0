/**
 * Inventory functionality for Market Stall Business Tracker - Updated with variant grouping
 */

/**
 * Load inventory page
 */
async function loadInventoryPage() {
    try {
        // First check Square setup status
        await checkSquareSetup();
        
        await loadInventoryData();
        await loadCategories();
        setupInventoryFilters();
    } catch (error) {
        console.error('Error loading inventory page:', error);
        showWarning('Failed to load inventory data');
    }
}

/**
 * Fix Square mapping by matching product names
 */
async function fixSquareMapping() {
    try {
        if (!confirm('This will attempt to fix product mapping by matching names with Square. Continue?')) {
            return;
        }
        
        showInfo('Fixing Square product mapping... This may take a moment.');
        
        const response = await apiRequest('/inventory/api/force-resync-square-names', {
            method: 'POST'
        });
        
        if (response.success) {
            showSuccess(`Successfully matched ${response.matched_count} products with Square variations!`);
            
            // Refresh the setup check and inventory data
            await checkSquareSetup();
            await loadInventoryData();
            
            // Suggest running debug to see results
            setTimeout(() => {
                if (confirm('Mapping fixed! Would you like to run diagnostics to see the results?')) {
                    debugSquareMapping();
                }
            }, 2000);
        } else {
            showError('Failed to fix mapping: ' + response.error);
        }
    } catch (error) {
        console.error('Error fixing Square mapping:', error);
        showError('Failed to fix Square mapping');
    }
}

/**
 * Debug Square mapping to identify sync issues
 */
async function debugSquareMapping() {
    try {
        showInfo('Running Square mapping diagnostics...');
        
        const response = await apiRequest('/inventory/api/debug-square-mapping');
        
        if (response.success) {
            const data = response.data;
            
            // Create debug report
            let report = `🔍 SQUARE MAPPING DEBUG REPORT\n`;
            report += `====================================\n\n`;
            
            report += `📊 SUMMARY:\n`;
            report += `- Total products: ${data.total_products}\n`;
            report += `- Mapped to Square: ${data.mapped_products.length}\n`;
            report += `- Unmapped: ${data.unmapped_products.length}\n\n`;
            
            if (data.mapped_products.length > 0) {
                report += `✅ MAPPED PRODUCTS:\n`;
                data.mapped_products.forEach(product => {
                    report += `- "${product.name}"\n`;
                    report += `  Square ID: ${product.square_variation_id}\n`;
                    report += `  Local Stock: ${product.current_stock}\n`;
                    if ('square_stock' in product) {
                        report += `  Square Stock: ${product.square_stock}\n`;
                    }
                    report += `\n`;
                });
            }
            
            if (data.unmapped_products.length > 0) {
                report += `❌ UNMAPPED PRODUCTS:\n`;
                data.unmapped_products.slice(0, 10).forEach(product => {
                    report += `- "${product.name}"\n`;
                });
                if (data.unmapped_products.length > 10) {
                    report += `... and ${data.unmapped_products.length - 10} more\n`;
                }
                report += `\n`;
            }
            
            if (data.square_api_test) {
                report += `🔌 SQUARE API TEST:\n`;
                if (data.square_api_test.success) {
                    report += `✅ API working - returned data for ${Object.keys(data.square_api_test.returned_data).length} items\n`;
                    
                    // Show sample data
                    const samples = Object.entries(data.square_api_test.returned_data).slice(0, 3);
                    samples.forEach(([id, stock]) => {
                        report += `  ${id}: ${stock} units\n`;
                    });
                } else {
                    report += `❌ API failed: ${data.square_api_test.error || 'Unknown error'}\n`;
                }
            }
            
            // Show in console and alert
            console.log(report);
            alert(report);
            
            // Also show recommendations
            if (data.unmapped_products.length > 0) {
                showWarning(`${data.unmapped_products.length} products are not mapped to Square. Run 'Sync Catalog' to fix this.`);
            } else if (data.square_api_test && !data.square_api_test.success) {
                showError('Square API test failed. Check your API credentials.');
            } else {
                showSuccess('Debug complete - check console for detailed report');
            }
        } else {
            showError('Debug failed: ' + response.error);
        }
    } catch (error) {
        console.error('Error running debug:', error);
        showError('Failed to run debug diagnostics');
    }
}

/**
 * Check Square integration setup and show appropriate UI
 */
async function checkSquareSetup() {
    try {
        const response = await apiRequest('/inventory/api/check-square-setup');
        
        const squareSection = document.getElementById('square-sync-section');
        if (!squareSection) {
            // Create Square sync section if it doesn't exist
            createSquareSyncSection();
        }
        
        if (response.success) {
            const data = response.data;
            updateSquareSyncUI(data);
        } else {
            showSquareSetupError(response.error, response.fix);
        }
    } catch (error) {
        console.error('Error checking Square setup:', error);
        showSquareSetupError('Unable to check Square setup');
    }
}

/**
 * Create Square sync section in the UI
 */
function createSquareSyncSection() {
    const inventoryView = document.getElementById('inventory-view');
    if (!inventoryView) return;
    
    const alertsSection = document.getElementById('inventory-alerts-section');
    
    const squareSection = document.createElement('div');
    squareSection.id = 'square-sync-section';
    squareSection.className = 'bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6';
    
    // Insert after alerts section
    if (alertsSection && alertsSection.nextSibling) {
        inventoryView.insertBefore(squareSection, alertsSection.nextSibling);
    } else if (alertsSection) {
        alertsSection.parentNode.insertBefore(squareSection, alertsSection.nextSibling);
    } else {
        // Insert at the beginning if no alerts section
        inventoryView.insertBefore(squareSection, inventoryView.firstChild);
    }
}

/**
 * Update Square sync UI based on setup status
 */
function updateSquareSyncUI(data) {
    const section = document.getElementById('square-sync-section');
    if (!section) return;
    
    if (data.needs_catalog_sync) {
        section.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="flex-1">
                    <p class="font-medium text-blue-900">Square Setup Required</p>
                    <p class="text-sm text-blue-700">Sync your Square product catalog first to enable inventory tracking</p>
                </div>
                <button onclick="syncSquareCatalog()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Sync Catalog
                </button>
            </div>
        `;
    } else if (data.can_sync_inventory) {
        section.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="flex-1">
                    <p class="font-medium text-green-900">Square Integration Active</p>
                    <p class="text-sm text-green-700">${data.mapped_products} of ${data.total_products} products mapped to Square</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="debugSquareMapping()" class="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        Debug
                    </button>
                    <button onclick="fixComprehensiveMapping()" class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm">
                        Fix Mapping
                    </button>
                    <button onclick="enableSquareInventoryTracking()" class="px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm">
                        Enable Tracking
                    </button>
                    <button onclick="syncStockFromSquare()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        Sync Stock Levels
                    </button>
                </div>
            </div>
        `;
    } else {
        section.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="flex-1">
                    <p class="font-medium text-yellow-900">Square Setup Incomplete</p>
                    <p class="text-sm text-yellow-700">Configure Square integration in Settings to sync inventory</p>
                </div>
                <a href="/settings/" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                    Go to Settings
                </a>
            </div>
        `;
    }
}

/**
 * Show Square setup error
 */
function showSquareSetupError(error, fix) {
    const section = document.getElementById('square-sync-section');
    if (!section) return;
    
    let fixButton = '';
    if (fix === 'restart_app') {
        fixButton = '<button onclick="window.location.reload()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">Restart App</button>';
    } else if (fix === 'configure_square') {
        fixButton = '<a href="/settings/" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">Configure Square</a>';
    }
    
    section.innerHTML = `
        <div class="flex items-center gap-3">
            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div class="flex-1">
                <p class="font-medium text-red-900">Square Setup Error</p>
                <p class="text-sm text-red-700">${error}</p>
            </div>
            ${fixButton}
        </div>
    `;
}

/**
 * Sync Square catalog (products and variations)
 */
async function syncSquareCatalog() {
    try {
        showInfo('Syncing product catalog from Square... This may take a moment.');
        
        const response = await apiRequest('/inventory/api/sync-square-catalog', {
            method: 'POST'
        });
        
        if (response.success) {
            showSuccess(`Successfully synced ${response.synced_count} products from Square!`);
            
            // Refresh the setup check and inventory data
            await checkSquareSetup();
            await loadInventoryData();
        } else {
            showError('Failed to sync catalog: ' + response.error);
        }
    } catch (error) {
        console.error('Error syncing Square catalog:', error);
        showError('Failed to sync catalog from Square');
    }
}

/**
 * Load inventory data with grouped variants
 */
async function loadInventoryData() {
    try {
        showLoading('products-table-body');
        
        const response = await apiRequest('/inventory/api/products/grouped');
        
        if (response.success) {
            updateProductsTable(response.data);
            updateProductsCount(calculateTotalProductCount(response.data));
            checkInventoryAlerts(response.data);
        } else {
            showError('products-table-body', 'Failed to load products');
        }
    } catch (error) {
        console.error('Error loading inventory data:', error);
        showError('products-table-body', 'Failed to load products');
    }
}

/**
 * Calculate total product count including variants
 */
function calculateTotalProductCount(productGroups) {
    return productGroups.reduce((total, group) => {
        return total + 1 + group.variant_count; // parent + variants
    }, 0);
}

/**
 * Update products table with grouped variants
 */
function updateProductsTable(productGroups) {
    const tbody = document.getElementById('products-table-body');
    if (!tbody) return;
    
    if (productGroups.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center py-8 text-gray-500">No products found</td></tr>';
        return;
    }
    
    tbody.innerHTML = productGroups.map(group => {
        const parent = group.parent;
        const hasVariants = group.has_variants;
        const variants = group.variants || [];
        
        let html = renderProductRow(parent, true, hasVariants, variants.length);
        
        // Add variant rows if they exist
        if (hasVariants && variants.length > 0) {
            const variantRows = variants.map(variant => 
                renderProductRow(variant, false, false, 0, parent.id)
            ).join('');
            
            html += `
                <tr class="variant-container hidden" data-parent-id="${parent.id}">
                    <td colspan="7" class="p-0">
                        <table class="w-full">
                            <tbody>
                                ${variantRows}
                            </tbody>
                        </table>
                    </td>
                </tr>
            `;
        }
        
        return html;
    }).join('');
}

/**
 * Render individual product row
 */
function renderProductRow(product, isParent = true, hasVariants = false, variantCount = 0, parentId = null) {
    const stockStatus = getStockStatus(product);
    const profitMarginClass = getProfitMarginClass(product.avg_profit_margin);
    const isVariant = !isParent;
    
    return `
        <tr class="product-table-row ${stockStatus.class} ${isVariant ? 'variant-row bg-gray-50' : ''}" ${isParent ? `data-product-id="${product.id}"` : ''}>
            <td>
                <div class="flex items-center ${isVariant ? 'pl-8' : ''}">
                    ${isParent && hasVariants ? `
                        <button onclick="toggleVariants(${product.id})" 
                                class="mr-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                                title="Show/hide variants">
                            <svg class="w-4 h-4 expand-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    ` : ''}
                    ${!isParent && !hasVariants ? '<div class="w-6 mr-2"></div>' : ''}
                    <div>
                        <div class="font-medium ${isVariant ? 'text-sm text-gray-700' : ''}">
                            ${isVariant ? product.name.split(' - ').slice(1).join(' - ') : product.name}
                        </div>
                        ${product.is_consignment ? '<div class="text-xs text-purple-600">Consignment</div>' : ''}
                        ${isParent && hasVariants ? `<div class="text-xs text-blue-600">${variantCount} variants</div>` : ''}
                    </div>
                </div>
            </td>
            <td>
                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                    ${product.category || 'No category'}
                </span>
            </td>
            <td>
                <div class="flex items-center">
                    <span class="mr-2">${product.current_stock}</span>
                    <span class="stock-indicator ${stockStatus.indicator}">
                        ${stockStatus.text}
                    </span>
                </div>
                <div class="text-xs text-gray-500">Reorder: ${product.reorder_level}</div>
            </td>
            <td>
                <div class="font-medium">${formatCurrency(product.total_cogs)}</div>
                ${product.total_cogs > 0 ? 
                    `<div class="text-xs text-gray-500">
                        Materials: ${formatCurrency(product.raw_materials_cost + product.assembly_parts_cost)}<br>
                        Labor: ${formatCurrency((product.labor_hours || 0) * (product.labor_rate || 0))}
                    </div>` : 
                    `<div class="text-xs text-gray-500">
                        ${isParent && hasVariants ? 'Set on variants' : 'Not set'}
                    </div>`
                }
            </td>
            <td>
                ${product.avg_profit_margin ? 
                    `<span class="profit-margin ${profitMarginClass}">${formatPercentage(product.avg_profit_margin)}</span>` : 
                    '<span class="text-gray-400">-</span>'
                }
            </td>
            <td>
                <span class="px-2 py-1 text-xs rounded-full ${product.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${product.active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td>
                <div class="flex space-x-2">
                    <button onclick="openCOGSModal(${product.id}, '${product.name.replace(/'/g, "\\'")}', '${product.category || ''}')" 
                            class="text-blue-600 hover:text-blue-900 text-sm">
                        ${product.total_cogs > 0 ? 'Edit COGS' : 'Set COGS'}
                    </button>
                    <button onclick="editProduct(${product.id})" 
                            class="text-green-600 hover:text-green-900 text-sm">
                        Edit
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * Toggle variant visibility
 */
function toggleVariants(parentId) {
    const variantContainer = document.querySelector(`tr.variant-container[data-parent-id="${parentId}"]`);
    const expandIcon = document.querySelector(`tr[data-product-id="${parentId}"] .expand-icon`);
    
    if (!variantContainer || !expandIcon) return;
    
    const isHidden = variantContainer.classList.contains('hidden');
    
    if (isHidden) {
        // Show variants
        variantContainer.classList.remove('hidden');
        expandIcon.style.transform = 'rotate(90deg)';
    } else {
        // Hide variants
        variantContainer.classList.add('hidden');
        expandIcon.style.transform = 'rotate(0deg)';
    }
}

/**
 * Get stock status
 */
function getStockStatus(product) {
    if (product.current_stock <= 0) {
        return { class: 'out-of-stock', indicator: 'out', text: 'Out' };
    } else if (product.current_stock <= product.reorder_level) {
        return { class: 'low-stock', indicator: 'low', text: 'Low' };
    } else {
        return { class: '', indicator: 'good', text: 'Good' };
    }
}

/**
 * Get profit margin CSS class
 */
function getProfitMarginClass(profitMargin) {
    if (!profitMargin) return '';
    
    if (profitMargin >= 30) {
        return 'high';
    } else if (profitMargin >= 15) {
        return 'medium';
    } else {
        return 'low';
    }
}

/**
 * Update products count
 */
function updateProductsCount(count) {
    const countElement = document.getElementById('products-count');
    if (countElement) {
        countElement.textContent = count;
    }
}

/**
 * Check inventory alerts - updated to work with grouped data
 */
function checkInventoryAlerts(productGroups) {
    const alertsSection = document.getElementById('inventory-alerts-section');
    const alertMessage = document.getElementById('low-stock-message');
    
    if (!alertsSection || !alertMessage) return;
    
    // Flatten the grouped products to check stock levels
    const allProducts = [];
    productGroups.forEach(group => {
        allProducts.push(group.parent);
        if (group.variants) {
            allProducts.push(...group.variants);
        }
    });
    
    const lowStockProducts = allProducts.filter(p => p.current_stock <= p.reorder_level && p.active);
    
    if (lowStockProducts.length > 0) {
        alertMessage.textContent = `${lowStockProducts.length} products need restocking: ${lowStockProducts.slice(0, 3).map(p => p.name).join(', ')}${lowStockProducts.length > 3 ? '...' : ''}`;
        alertsSection.classList.remove('hidden');
    } else {
        alertsSection.classList.add('hidden');
    }
}

/**
 * Load categories
 */
async function loadCategories() {
    try {
        const response = await apiRequest('/inventory/api/products/categories');
        
        if (response.success) {
            const categoryFilter = document.getElementById('category-filter');
            if (categoryFilter) {
                categoryFilter.innerHTML = '<option value="">All Categories</option>' +
                    response.data.map(category => `<option value="${category}">${category}</option>`).join('');
            }
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

/**
 * Load categories into a select element
 */
async function loadCategoriesIntoSelect(selectId, selectedValue = '') {
    try {
        const response = await apiRequest('/inventory/api/products/categories');
        
        if (response.success) {
            const select = document.getElementById(selectId);
            if (select) {
                select.innerHTML = '<option value="">No category</option>' +
                    response.data.map(category => 
                        `<option value="${category}"${category === selectedValue ? ' selected' : ''}>${category}</option>`
                    ).join('');
            }
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

/**
 * Setup inventory filters
 */
function setupInventoryFilters() {
    const searchInput = document.getElementById('product-search');
    const categoryFilter = document.getElementById('category-filter');
    const stockFilter = document.getElementById('stock-filter');
    const showInactive = document.getElementById('show-inactive');
    
    const applyFilters = debounce(() => {
        // TODO: Implement filtering logic for grouped data
        loadInventoryData();
    }, 300);
    
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', applyFilters);
    }
    
    if (stockFilter) {
        stockFilter.addEventListener('change', applyFilters);
    }
    
    if (showInactive) {
        showInactive.addEventListener('change', applyFilters);
    }
}

/**
 * Edit product
 */
async function editProduct(productId) {
    try {
        // Get product data
        const response = await apiRequest(`/inventory/api/products/${productId}`);
        
        if (response.success) {
            const product = response.data;
            
            // Fill form with existing data
            document.getElementById('edit-product-id').value = product.id;
            document.getElementById('edit-product-name').value = product.name;
            document.getElementById('edit-product-category').value = product.category || '';
            document.getElementById('edit-current-stock').value = product.current_stock;
            document.getElementById('edit-reorder-level').value = product.reorder_level;
            document.getElementById('edit-square-product-id').value = product.square_product_id || '';
            document.getElementById('edit-product-active').checked = product.active;
            
            // Load categories into the select
            await loadCategoriesIntoSelect('edit-product-category', product.category);
            
            // Show modal
            document.getElementById('edit-product-modal').classList.remove('hidden');
        } else {
            showWarning('Failed to load product data: ' + response.error);
        }
    } catch (error) {
        console.error('Error loading product:', error);
        showWarning('Failed to load product data');
    }
}

/**
 * Close edit product modal
 */
function closeEditProductModal() {
    document.getElementById('edit-product-modal').classList.add('hidden');
}

/**
 * Update product
 */
async function updateProduct(event) {
    event.preventDefault();
    
    const productId = document.getElementById('edit-product-id').value;
    const formData = {
        name: document.getElementById('edit-product-name').value.trim(),
        category: document.getElementById('edit-product-category').value,
        current_stock: parseInt(document.getElementById('edit-current-stock').value) || 0,
        reorder_level: parseInt(document.getElementById('edit-reorder-level').value) || 5,
        square_product_id: document.getElementById('edit-square-product-id').value.trim(),
        active: document.getElementById('edit-product-active').checked
    };
    
    // Basic validation
    if (!formData.name) {
        showWarning('Product name is required');
        return;
    }
    
    try {
        const response = await apiRequest(`/inventory/api/products/${productId}`, {
            method: 'PUT',
            body: JSON.stringify(formData)
        });
        
        if (response.success) {
            showSuccess('Product updated successfully');
            closeEditProductModal();
            await loadInventoryData(); // Reload the table
        } else {
            showWarning('Failed to update product: ' + response.error);
        }
    } catch (error) {
        console.error('Error updating product:', error);
        showWarning('Failed to update product');
    }
}

/**
 * Show add product modal
 */
function showAddProductModal() {
    // Reset form
    document.getElementById('add-product-form').reset();
    
    // Load categories into the select
    loadCategoriesIntoSelect('product-category');
    
    // Show modal
    document.getElementById('add-product-modal').classList.remove('hidden');
}

/**
 * Close add product modal
 */
function closeAddProductModal() {
    document.getElementById('add-product-modal').classList.add('hidden');
}

/**
 * Add new product
 */
async function addProduct(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('product-name').value.trim(),
        category: document.getElementById('product-category').value,
        current_stock: parseInt(document.getElementById('initial-stock').value) || 0,
        reorder_level: parseInt(document.getElementById('reorder-level').value) || 5,
        square_product_id: document.getElementById('square-product-id').value.trim(),
        active: document.getElementById('product-active').checked
    };
    
    // Basic validation
    if (!formData.name) {
        showWarning('Product name is required');
        return;
    }
    
    try {
        const response = await apiRequest('/inventory/api/products', {
            method: 'POST',
            body: JSON.stringify(formData)
        });
        
        if (response.success) {
            showSuccess('Product added successfully');
            closeAddProductModal();
            await loadInventoryData(); // Reload the table
        } else {
            showWarning('Failed to add product: ' + response.error);
        }
    } catch (error) {
        console.error('Error adding product:', error);
        showWarning('Failed to add product');
    }
}

/**
 * Export inventory
 */
function exportInventory() {
    window.open('/settings/api/settings/export/products', '_blank');
}

/**
 * Bulk update COGS
 */
function bulkUpdateCOGS() {
    showWarning('Bulk COGS update functionality coming soon!');
}

/**
 * Bulk update categories
 */
function bulkUpdateCategories() {
    showWarning('Bulk category update functionality coming soon!');
}

/**
 * Generate stock report
 */
function generateStockReport() {
    showWarning('Stock report functionality coming soon!');
}

/**
 * Manage variant COGS for parent products
 */
function manageVariantCOGS(parentId) {
    // Expand variants first if they're hidden
    const variantContainer = document.querySelector(`tr.variant-container[data-parent-id="${parentId}"]`);
    if (variantContainer && variantContainer.classList.contains('hidden')) {
        toggleVariants(parentId);
    }
    
    showInfo('Click "Set COGS" on individual variants below to set their costs separately.');
}

/**
 * Delete product
 */
async function deleteProduct(productId, productName) {
    if (!confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        const response = await apiRequest(`/inventory/api/products/${productId}`, 'DELETE');
        
        if (response.success) {
            showSuccess('Product deleted successfully');
            await loadInventoryData(); // Reload the table
        } else {
            showError('Failed to delete product: ' + response.error);
        }
    } catch (error) {
        console.error('Error deleting product:', error);
        showError('Failed to delete product');
    }
}

/**
 * Create new product manually
 */
function createNewProduct() {
    showAddProductModal();
}

/**
 * Sync stock levels from Square
 */
async function syncStockFromSquare() {
    try {
        showInfo('Syncing stock levels from Square... This may take a moment.');

        const response = await apiRequest('/inventory/api/sync-square-stock', {
            method: 'POST'
        });

        if (response.success) {
            showSuccess(response.message);

            // Refresh the inventory data to show updated stock
            await loadInventoryData();

            // Refresh Square setup status
            await checkSquareSetup();
        } else {
            showError('Failed to sync stock: ' + response.error);
        }
    } catch (error) {
        console.error('Error syncing stock:', error);
        showError('Failed to sync stock from Square');
    }
}

/**
 * Enable inventory tracking for all products in Square
 */
async function enableSquareInventoryTracking() {
    try {
        if (!confirm('This will enable inventory tracking for all products in Square and set their initial stock levels. This may take several minutes. Continue?')) {
            return;
        }

        showInfo('Enabling inventory tracking in Square... This may take several minutes.');

        const response = await apiRequest('/inventory/api/enable-square-inventory-tracking', {
            method: 'POST'
        });

        if (response.success) {
            showSuccess(`Successfully enabled inventory tracking for ${response.enabled_count} products in Square!`);

            // Suggest running stock sync after enabling tracking
            setTimeout(() => {
                if (confirm('Inventory tracking enabled! Would you like to sync stock levels now?')) {
                    syncStockFromSquare();
                }
            }, 2000);
        } else {
            showError('Failed to enable inventory tracking: ' + response.error);
        }
    } catch (error) {
        console.error('Error enabling inventory tracking:', error);
        showError('Failed to enable inventory tracking in Square');
    }
}

/**
 * Fix comprehensive Square product mapping
 */
async function fixComprehensiveMapping() {
    try {
        if (!confirm('This will fix product mapping issues by properly matching your local products to Square variations. This may take a moment. Continue?')) {
            return;
        }

        showInfo('Fixing product mapping... This may take a moment.');

        const response = await apiRequest('/inventory/api/fix-square-mapping-comprehensive', {
            method: 'POST'
        });

        if (response.success) {
            showSuccess(`Successfully fixed mapping for ${response.fixed_count} products!`);

            // Refresh the setup check and inventory data
            await checkSquareSetup();
            await loadInventoryData();

            // Suggest running stock sync after fixing mapping
            setTimeout(() => {
                if (confirm('Mapping fixed! Would you like to sync stock levels now?')) {
                    syncStockFromSquare();
                }
            }, 2000);
        } else {
            showError('Failed to fix mapping: ' + response.error);
        }
    } catch (error) {
        console.error('Error fixing mapping:', error);
        showError('Failed to fix product mapping');
    }
}

// Set up form event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add product form
    const addProductForm = document.getElementById('add-product-form');
    if (addProductForm) {
        addProductForm.addEventListener('submit', addProduct);
    }
    
    // Edit product form
    const editProductForm = document.getElementById('edit-product-form');
    if (editProductForm) {
        editProductForm.addEventListener('submit', updateProduct);
    }
});
