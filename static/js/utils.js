/**
 * Utility functions for Market Stall Business Tracker
 */

// Global variables
let currentView = 'dashboard';
let cache = {};

/**
 * Format currency values
 */
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '$0.00';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Format percentage values
 */
function formatPercentage(value) {
    if (value === null || value === undefined) return '0.0%';
    return `${parseFloat(value).toFixed(1)}%`;
}

/**
 * Format numbers with commas
 */
function formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return new Intl.NumberFormat('en-US').format(num);
}

/**
 * Show loading spinner
 */
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '<div class="flex items-center justify-center py-8"><div class="loading-spinner"></div><span class="ml-2 text-gray-500">Loading...</span></div>';
    }
}

/**
 * Show error message
 */
function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="flex items-center justify-center py-8 text-red-600">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>${message}</span>
            </div>
        `;
    }
}

/**
 * Show success message
 */
function showSuccess(message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 fade-in';
    toast.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

/**
 * Show info message
 */
function showInfo(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 fade-in';
    toast.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

/**
 * Show warning message
 */
function showWarning(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-yellow-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 fade-in';
    toast.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 4000);
}

/**
 * Make API request with error handling
 */
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

/**
 * Update last updated timestamp
 */
function updateLastUpdated() {
    const element = document.getElementById('last-updated');
    if (element) {
        element.textContent = new Date().toLocaleString();
    }
}

/**
 * Show/hide views
 */
function showView(viewName) {
    // Hide all views
    const views = ['dashboard', 'sales', 'analytics', 'inventory', 'settings'];
    views.forEach(view => {
        const element = document.getElementById(`${view}-view`);
        if (element) {
            element.style.display = 'none';
        }
    });
    
    // Show selected view
    const selectedView = document.getElementById(`${viewName}-view`);
    if (selectedView) {
        selectedView.style.display = 'block';
        currentView = viewName;
    }
    
    // Update navigation
    updateNavigation(viewName);
    
    // Load view-specific data
    loadViewData(viewName);
}

/**
 * Update navigation active state
 */
function updateNavigation(activeView) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    const activeNavItem = document.getElementById(`nav-${activeView}`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
}

/**
 * Load view-specific data
 */
function loadViewData(viewName) {
    switch (viewName) {
        case 'dashboard':
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }
            break;
        case 'sales':
            if (typeof loadSalesPage === 'function') {
                loadSalesPage();
            }
            break;
        case 'analytics':
            if (typeof loadAnalyticsPage === 'function') {
                loadAnalyticsPage();
            }
            break;
        case 'inventory':
            if (typeof loadInventoryPage === 'function') {
                loadInventoryPage();
            }
            break;
        case 'settings':
            if (typeof loadSettingsPage === 'function') {
                loadSettingsPage();
            }
            break;
    }
}

/**
 * Get date range for common periods
 */
function getDateRange(period) {
    const today = new Date();
    const formatDate = (date) => date.toISOString().split('T')[0];
    
    switch (period) {
        case 'today':
            return { from: formatDate(today), to: formatDate(today) };
        
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return { from: formatDate(yesterday), to: formatDate(yesterday) };
        
        case 'week':
            const weekStart = new Date(today);
            weekStart.setDate(weekStart.getDate() - 7);
            return { from: formatDate(weekStart), to: formatDate(today) };
        
        case 'month':
            const monthStart = new Date(today);
            monthStart.setDate(monthStart.getDate() - 30);
            return { from: formatDate(monthStart), to: formatDate(today) };
        
        case 'quarter':
            const quarterStart = new Date(today);
            quarterStart.setDate(quarterStart.getDate() - 90);
            return { from: formatDate(quarterStart), to: formatDate(today) };
        
        default:
            return { from: formatDate(new Date(today.setDate(today.getDate() - 30))), to: formatDate(new Date()) };
    }
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Validate form data
 */
function validateForm(formData, rules) {
    const errors = {};
    
    for (const [field, rule] of Object.entries(rules)) {
        const value = formData[field];
        
        if (rule.required && (!value || value.toString().trim() === '')) {
            errors[field] = `${rule.label || field} is required`;
            continue;
        }
        
        if (value && rule.type === 'number') {
            const num = parseFloat(value);
            if (isNaN(num)) {
                errors[field] = `${rule.label || field} must be a number`;
                continue;
            }
            
            if (rule.min !== undefined && num < rule.min) {
                errors[field] = `${rule.label || field} must be at least ${rule.min}`;
                continue;
            }
            
            if (rule.max !== undefined && num > rule.max) {
                errors[field] = `${rule.label || field} must be at most ${rule.max}`;
                continue;
            }
        }
        
        if (value && rule.type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                errors[field] = `${rule.label || field} must be a valid email`;
            }
        }
    }
    
    return Object.keys(errors).length === 0 ? null : errors;
}

/**
 * Clear form errors
 */
function clearFormErrors(formId) {
    const form = document.getElementById(formId);
    if (form) {
        const errorElements = form.querySelectorAll('.error-message');
        errorElements.forEach(el => el.remove());
        
        const inputElements = form.querySelectorAll('.form-input.error');
        inputElements.forEach(el => el.classList.remove('error'));
    }
}

/**
 * Show form errors
 */
function showFormErrors(formId, errors) {
    clearFormErrors(formId);
    
    for (const [field, message] of Object.entries(errors)) {
        const input = document.getElementById(field);
        if (input) {
            input.classList.add('error');
            
            const errorElement = document.createElement('div');
            errorElement.className = 'error-message text-red-600 text-sm mt-1';
            errorElement.textContent = message;
            
            input.parentNode.appendChild(errorElement);
        }
    }
}
