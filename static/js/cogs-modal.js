/**
 * COGS Modal functionality for Market Stall Business Tracker
 */

let currentCOGSProductId = null;

/**
 * Open COGS modal for a product
 */
function openCOGSModal(productId, productName, productCategory) {
    currentCOGSProductId = productId;
    
    // Set product info
    document.getElementById('cogs-product-id').value = productId;
    document.getElementById('cogs-product-name').textContent = productName;
    document.getElementById('cogs-product-category').textContent = productCategory;
    
    // Load existing COGS data
    loadCOGSData(productId);
    
    // Show modal
    document.getElementById('cogs-modal').classList.remove('hidden');
    
    // Set up real-time calculation
    setupCOGSCalculation();
}

/**
 * Close COGS modal
 */
function closeCOGSModal() {
    document.getElementById('cogs-modal').classList.add('hidden');
    currentCOGSProductId = null;
    
    // Reset form
    document.getElementById('cogs-form').reset();
    document.getElementById('consignment-details').classList.add('hidden');
}

/**
 * Load existing COGS data for a product
 */
async function loadCOGSData(productId) {
    try {
        const response = await apiRequest(`/inventory/api/products/${productId}`);
        
        if (response.success) {
            const product = response.data;
            
            // Fill form with existing data
            document.getElementById('raw-materials-cost').value = product.raw_materials_cost || 0;
            document.getElementById('assembly-parts-cost').value = product.assembly_parts_cost || 0;
            document.getElementById('labor-hours').value = product.labor_hours || 0;
            document.getElementById('labor-rate').value = product.labor_rate || 15;
            document.getElementById('packaging-cost').value = product.packaging_cost || 0;
            document.getElementById('overhead-cost').value = product.overhead_cost || 0;
            
            // Handle consignment
            document.getElementById('is-consignment').checked = product.is_consignment || false;
            document.getElementById('consignment-partner').value = product.consignment_partner || '';
            document.getElementById('consignment-split').value = product.consignment_split || 0.5;
            
            if (product.is_consignment) {
                document.getElementById('consignment-details').classList.remove('hidden');
            }
            
            // Calculate totals
            calculateCOGSTotals();
        }
    } catch (error) {
        console.error('Error loading COGS data:', error);
        showWarning('Failed to load existing COGS data');
    }
}

/**
 * Setup real-time COGS calculation
 */
function setupCOGSCalculation() {
    const inputs = [
        'raw-materials-cost',
        'assembly-parts-cost', 
        'labor-hours',
        'labor-rate',
        'packaging-cost',
        'overhead-cost'
    ];
    
    inputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', calculateCOGSTotals);
        }
    });
    
    // Consignment checkbox
    const consignmentCheckbox = document.getElementById('is-consignment');
    if (consignmentCheckbox) {
        consignmentCheckbox.addEventListener('change', function() {
            const details = document.getElementById('consignment-details');
            if (this.checked) {
                details.classList.remove('hidden');
            } else {
                details.classList.add('hidden');
            }
        });
    }
    
    // Initial calculation
    calculateCOGSTotals();
}

/**
 * Calculate COGS totals in real-time
 */
function calculateCOGSTotals() {
    const rawMaterials = parseFloat(document.getElementById('raw-materials-cost').value) || 0;
    const assemblyParts = parseFloat(document.getElementById('assembly-parts-cost').value) || 0;
    const laborHours = parseFloat(document.getElementById('labor-hours').value) || 0;
    const laborRate = parseFloat(document.getElementById('labor-rate').value) || 0;
    const packaging = parseFloat(document.getElementById('packaging-cost').value) || 0;
    const overhead = parseFloat(document.getElementById('overhead-cost').value) || 0;
    
    const laborCost = laborHours * laborRate;
    const totalCOGS = rawMaterials + assemblyParts + laborCost + packaging + overhead;
    
    // Update summary
    document.getElementById('summary-materials').textContent = formatCurrency(rawMaterials);
    document.getElementById('summary-assembly').textContent = formatCurrency(assemblyParts);
    document.getElementById('summary-labor').textContent = formatCurrency(laborCost);
    document.getElementById('summary-other').textContent = formatCurrency(packaging + overhead);
    document.getElementById('total-cogs').textContent = formatCurrency(totalCOGS);
}

/**
 * Save COGS data
 */
async function saveCOGS(event) {
    event.preventDefault();
    
    if (!currentCOGSProductId) {
        showWarning('No product selected');
        return;
    }
    
    const formData = {
        raw_materials_cost: parseFloat(document.getElementById('raw-materials-cost').value) || 0,
        assembly_parts_cost: parseFloat(document.getElementById('assembly-parts-cost').value) || 0,
        labor_hours: parseFloat(document.getElementById('labor-hours').value) || 0,
        labor_rate: parseFloat(document.getElementById('labor-rate').value) || 0,
        packaging_cost: parseFloat(document.getElementById('packaging-cost').value) || 0,
        overhead_cost: parseFloat(document.getElementById('overhead-cost').value) || 0,
        is_consignment: document.getElementById('is-consignment').checked,
        consignment_partner: document.getElementById('consignment-partner').value,
        consignment_split: parseFloat(document.getElementById('consignment-split').value) || 0.5
    };
    
    try {
        const response = await apiRequest(`/inventory/api/products/${currentCOGSProductId}`, {
            method: 'PUT',
            body: JSON.stringify(formData)
        });
        
        if (response.success) {
            showSuccess('COGS updated successfully');
            closeCOGSModal();
            
            // Reload inventory data to show updated COGS
            if (typeof loadInventoryData === 'function') {
                await loadInventoryData();
            }
        } else {
            showWarning('Failed to update COGS: ' + response.error);
        }
    } catch (error) {
        console.error('Error saving COGS:', error);
        showWarning('Failed to save COGS data');
    }
}

// Set up form submission
document.addEventListener('DOMContentLoaded', function() {
    const cogsForm = document.getElementById('cogs-form');
    if (cogsForm) {
        cogsForm.addEventListener('submit', saveCOGS);
    }
});
