/**
 * Sales functionality for Market Stall Business Tracker
 */

/**
 * Load sales page
 */
async function loadSalesPage() {
    try {
        await Promise.all([
            loadProducts(),
            loadLocations(),
            loadRecentSales()
        ]);
        
        setupSalesForm();
        
        // Set today's date as default
        const dateInput = document.getElementById('sale-date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
        
    } catch (error) {
        console.error('Error loading sales page:', error);
        showWarning('Failed to load sales page data');
    }
}

/**
 * Load products for dropdown
 */
async function loadProducts() {
    try {
        const response = await apiRequest('/inventory/api/products');
        
        if (response.success) {
            const productSelect = document.getElementById('product-select');
            
            if (productSelect) {
                productSelect.innerHTML = '<option value="">Select a product...</option>' +
                    response.data.map(product => 
                        `<option value="${product.name}" data-cogs="${product.total_cogs || 0}">
                            ${product.name} ${product.category ? `(${product.category})` : ''}
                        </option>`
                    ).join('');
            }
        }
    } catch (error) {
        console.error('Error loading products:', error);
        // If products endpoint doesn't exist yet, create some default options
        const productSelect = document.getElementById('product-select');
        if (productSelect) {
            productSelect.innerHTML = `
                <option value="">Select a product...</option>
                <option value="Handmade Soap" data-cogs="3.50">Handmade Soap</option>
                <option value="Artisan Coffee" data-cogs="5.25">Artisan Coffee</option>
                <option value="Organic Honey" data-cogs="7.00">Organic Honey</option>
                <option value="Custom Jewelry" data-cogs="12.50">Custom Jewelry</option>
            `;
        }
    }
}

/**
 * Load locations for dropdown (Square + consignment)
 */
async function loadLocations() {
    try {
        const response = await apiRequest('/settings/api/settings/locations');
        
        if (response.success) {
            const locationSelect = document.getElementById('location-select');
            
            if (locationSelect) {
                locationSelect.innerHTML = '<option value="">Select location...</option>' +
                    response.data.map(location => {
                        const typeIcon = location.type === 'square' ? '🏪' : '🤝';
                        const consignmentInfo = location.is_consignment ? 
                            ` (${location.commission_type === 'percentage' ? 
                                location.commission_value + '%' : 
                                '$' + location.commission_value} to ${location.partner_name})` : '';
                        
                        return `<option value="${location.name}" data-type="${location.type}" data-consignment="${location.is_consignment}">
                            ${typeIcon} ${location.name}${consignmentInfo}
                        </option>`;
                    }).join('');
            }
            
            // Also update the sales filter dropdown
            const filterSelect = document.getElementById('sales-filter-location');
            if (filterSelect) {
                filterSelect.innerHTML = '<option value="">All Locations</option>' +
                    response.data.map(location => 
                        `<option value="${location.name}">${location.name}</option>`
                    ).join('');
            }
        }
    } catch (error) {
        console.error('Error loading locations:', error);
        // Provide fallback locations
        const locationSelect = document.getElementById('location-select');
        if (locationSelect) {
            locationSelect.innerHTML = `
                <option value="">Select location...</option>
                <option value="Main Market Stall">🏪 Main Market Stall</option>
                <option value="Weekend Farmers Market">🏪 Weekend Farmers Market</option>
                <option value="Festival Booth">🏪 Festival Booth</option>
                <option value="Pop-up Shop">🏪 Pop-up Shop</option>
            `;
        }
    }
}

/**
 * Load recent sales
 */
async function loadRecentSales() {
    try {
        showLoading('recent-sales-table');
        
        const response = await apiRequest('/sales/api/sales?limit=20');
        
        if (response.success) {
            updateRecentSalesTable(response.data);
        } else {
            showError('recent-sales-table', 'Failed to load recent sales');
        }
    } catch (error) {
        console.error('Error loading recent sales:', error);
        // Show empty state instead of error for now
        const container = document.getElementById('recent-sales-table');
        if (container) {
            container.innerHTML = '<p class="text-gray-500 text-center py-8">No sales recorded yet. Add your first sale above!</p>';
        }
    }
}

/**
 * Update recent sales table
 */
function updateRecentSalesTable(salesData) {
    const container = document.getElementById('recent-sales-table');
    if (!container) return;
    
    if (salesData.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-8">No sales recorded yet</p>';
        return;
    }
    
    const tableHtml = `
        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Revenue</th>
                        <th>Profit</th>
                        <th>Location</th>
                        <th>Payment</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${salesData.map(sale => `
                        <tr>
                            <td>
                                <div class="font-medium">${sale.date}</div>
                                <div class="text-sm text-gray-500">${sale.time}</div>
                            </td>
                            <td>
                                <div class="font-medium">${sale.product_name}</div>
                                <div class="text-sm text-gray-500">${sale.category || 'No category'}</div>
                            </td>
                            <td>${sale.quantity}</td>
                            <td>${formatCurrency(sale.total_amount)}</td>
                            <td class="${(sale.gross_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">
                                ${formatCurrency(sale.gross_profit || 0)}
                                ${sale.profit_margin ? `<div class="text-xs">(${formatPercentage(sale.profit_margin)})</div>` : ''}
                            </td>
                            <td>${sale.location}</td>
                            <td>
                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                                    ${sale.payment_method || 'Unknown'}
                                </span>
                            </td>
                            <td>
                                <button onclick="editSale(${sale.id})" class="text-blue-600 hover:text-blue-900 text-sm">
                                    Edit
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}

/**
 * Setup sales form
 */
function setupSalesForm() {
    const salesForm = document.getElementById('sales-form');
    const productSelect = document.getElementById('product-select');
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit-price');
    
    // Product selection change
    if (productSelect) {
        productSelect.addEventListener('change', function() {
            updateProfitPreview();
        });
    }
    
    // Quantity and price changes
    if (quantityInput) {
        quantityInput.addEventListener('input', updateProfitPreview);
    }
    
    if (unitPriceInput) {
        unitPriceInput.addEventListener('input', updateProfitPreview);
    }
    
    // Form submission
    if (salesForm) {
        salesForm.addEventListener('submit', handleSalesFormSubmit);
    }
}

/**
 * Update profit preview
 */
function updateProfitPreview() {
    const productSelect = document.getElementById('product-select');
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit-price');
    const profitPreview = document.getElementById('profit-preview');
    
    if (!productSelect || !quantityInput || !unitPriceInput || !profitPreview) return;
    
    const selectedOption = productSelect.options[productSelect.selectedIndex];
    const quantity = parseFloat(quantityInput.value) || 0;
    const unitPrice = parseFloat(unitPriceInput.value) || 0;
    const cogs = parseFloat(selectedOption.dataset.cogs) || 0;
    
    if (quantity > 0 && unitPrice > 0) {
        const totalRevenue = quantity * unitPrice;
        const totalCOGS = quantity * cogs;
        const grossProfit = totalRevenue - totalCOGS;
        const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;
        
        document.getElementById('preview-revenue').textContent = formatCurrency(totalRevenue);
        document.getElementById('preview-cogs').textContent = formatCurrency(totalCOGS);
        document.getElementById('preview-profit').textContent = formatCurrency(grossProfit);
        document.getElementById('preview-margin').textContent = formatPercentage(profitMargin);
        
        profitPreview.classList.remove('hidden');
    } else {
        profitPreview.classList.add('hidden');
    }
}

/**
 * Handle sales form submission
 */
async function handleSalesFormSubmit(e) {
    e.preventDefault();
    
    const formData = {
        product_name: document.getElementById('product-select').value,
        quantity: parseInt(document.getElementById('quantity').value),
        unit_price: parseFloat(document.getElementById('unit-price').value),
        location: document.getElementById('location-select').value,
        payment_method: document.getElementById('payment-method').value,
        date: document.getElementById('sale-date').value,
        notes: document.getElementById('sale-notes').value
    };
    
    // Validate form
    const errors = validateForm(formData, {
        product_name: { required: true, label: 'Product' },
        quantity: { required: true, type: 'number', min: 1, label: 'Quantity' },
        unit_price: { required: true, type: 'number', min: 0, label: 'Unit Price' },
        location: { required: true, label: 'Location' },
        date: { required: true, label: 'Date' }
    });
    
    if (errors) {
        showFormErrors('sales-form', errors);
        return;
    }
    
    try {
        const response = await apiRequest('/sales/api/sales', {
            method: 'POST',
            body: JSON.stringify(formData)
        });
        
        if (response.success) {
            showSuccess('Sale recorded successfully!');
            resetSalesForm();
            loadRecentSales(); // Refresh the table
        } else {
            showWarning('Error recording sale: ' + (response.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error recording sale:', error);
        showWarning('Error recording sale: ' + error.message);
    }
}

/**
 * Reset sales form
 */
function resetSalesForm() {
    const form = document.getElementById('sales-form');
    if (form) {
        form.reset();
        
        // Set today's date
        const dateInput = document.getElementById('sale-date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
        
        // Hide profit preview
        const profitPreview = document.getElementById('profit-preview');
        if (profitPreview) {
            profitPreview.classList.add('hidden');
        }
        
        clearFormErrors('sales-form');
    }
}

/**
 * Edit sale
 */
function editSale(saleId) {
    // TODO: Implement edit functionality
    showWarning('Edit functionality coming soon!');
}

/**
 * Sync Square data
 */
async function syncSquareData() {
    try {
        showSuccess('Starting comprehensive Square sync...');
        
        const response = await apiRequest('/sales/api/sales/square-sync', {
            method: 'POST'
        });
        
        if (response.success) {
            const message = response.message || 
                `✅ Synced ${response.products_synced} products, ${response.orders_synced} orders, ${response.locations_synced} locations`;
            
            showSuccess(message);
            
            // Show detailed breakdown if multiple types synced
            if (response.products_synced > 0 || response.orders_synced > 0) {
                setTimeout(() => {
                    showSuccess(`📊 Products: ${response.products_synced} | 🛒 Orders: ${response.orders_synced} | 📍 Locations: ${response.locations_synced}`);
                }, 2000);
            }
            
            // Show any errors as warnings
            if (response.errors && response.errors.length > 0) {
                setTimeout(() => {
                    showWarning(`⚠️ ${response.errors.length} warnings occurred during sync`);
                }, 3000);
            }
            
            loadRecentSales(); // Refresh the sales table
        } else {
            showWarning('Square sync failed: ' + (response.error || response.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error syncing Square data:', error);
        showWarning('Square sync error: ' + error.message);
    }
}
