/**
 * Settings functionality for Market Stall Business Tracker
 */

/**
 * Load settings page
 */
async function loadSettingsPage() {
    try {
        await loadSquareSettings();
        await loadBusinessSettings();
        await loadAdvancedSettings();
        await loadDatabaseStats();
        updateSquareStatus();
    } catch (error) {
        console.error('Error loading settings page:', error);
        showWarning('Failed to load settings data');
    }
}

/**
 * Load Square settings
 */
async function loadSquareSettings() {
    try {
        const response = await apiRequest('/settings/api/settings?category=square');
        
        if (response.success) {
            const settings = response.data.reduce((acc, setting) => {
                acc[setting.key] = setting.value;
                return acc;
            }, {});
            
            const appIdInput = document.getElementById('square-app-id');
            const accessTokenInput = document.getElementById('square-access-token');
            const environmentSelect = document.getElementById('square-environment');
            
            if (appIdInput) appIdInput.value = settings.application_id || '';
            if (accessTokenInput) accessTokenInput.value = settings.access_token || '';
            if (environmentSelect) environmentSelect.value = settings.environment || 'sandbox';
        }
    } catch (error) {
        console.error('Error loading Square settings:', error);
    }
}

/**
 * Load business settings
 */
async function loadBusinessSettings() {
    try {
        const response = await apiRequest('/settings/api/settings?category=business');
        
        if (response.success) {
            const settings = response.data.reduce((acc, setting) => {
                acc[setting.key] = setting.value;
                return acc;
            }, {});
            
            const laborRateInput = document.getElementById('default-labor-rate');
            const consignmentSplitSelect = document.getElementById('default-consignment-split');
            const businessNameInput = document.getElementById('business-name');
            const locationsTextarea = document.getElementById('default-locations');
            
            if (laborRateInput) laborRateInput.value = settings.default_labor_rate || '15.00';
            if (consignmentSplitSelect) consignmentSplitSelect.value = settings.default_consignment_split || '0.5';
            if (businessNameInput) businessNameInput.value = settings.business_name || '';
            if (locationsTextarea) locationsTextarea.value = settings.default_locations || '';
        }
    } catch (error) {
        console.error('Error loading business settings:', error);
    }
}

/**
 * Load advanced settings
 */
async function loadAdvancedSettings() {
    try {
        const response = await apiRequest('/settings/api/settings?category=advanced');
        
        if (response.success) {
            const settings = response.data.reduce((acc, setting) => {
                acc[setting.key] = setting.value;
                return acc;
            }, {});
            
            const cacheTimeoutInput = document.getElementById('cache-timeout');
            const autoSyncSelect = document.getElementById('auto-sync-interval');
            const debugModeCheckbox = document.getElementById('debug-mode');
            
            if (cacheTimeoutInput) cacheTimeoutInput.value = settings.cache_timeout || '300';
            if (autoSyncSelect) autoSyncSelect.value = settings.auto_sync_interval || '1';
            if (debugModeCheckbox) debugModeCheckbox.checked = settings.debug_mode === 'true';
        }
    } catch (error) {
        console.error('Error loading advanced settings:', error);
    }
}

/**
 * Load database stats
 */
async function loadDatabaseStats() {
    try {
        const response = await apiRequest('/settings/api/settings/database/stats');
        
        if (response.success) {
            const container = document.getElementById('database-stats');
            if (container) {
                container.innerHTML = Object.entries(response.data).map(([table, count]) => `
                    <div class="text-center">
                        <div class="font-medium text-gray-900">${formatNumber(count)}</div>
                        <div class="text-sm text-gray-500">${table}</div>
                    </div>
                `).join('');
            }
        }
    } catch (error) {
        console.error('Error loading database stats:', error);
    }
}

/**
 * Update Square status
 */
function updateSquareStatus() {
    const statusElement = document.getElementById('square-status');
    if (!statusElement) return;
    
    const accessToken = document.getElementById('square-access-token')?.value;
    
    if (accessToken) {
        statusElement.className = 'connection-status testing';
        statusElement.textContent = 'Testing...';
        
        // Test connection automatically
        testSquareConnection();
    } else {
        statusElement.className = 'connection-status disconnected';
        statusElement.textContent = 'Not Connected';
    }
}

/**
 * Test Square connection
 */
async function testSquareConnection() {
    try {
        const statusElement = document.getElementById('square-status');
        const resultElement = document.getElementById('square-test-result');
        
        if (statusElement) {
            statusElement.className = 'connection-status testing';
            statusElement.textContent = 'Testing...';
        }
        
        const response = await apiRequest('/settings/api/settings/square/test-connection', {
            method: 'POST'
        });
        
        if (resultElement) {
            resultElement.classList.remove('hidden');
            
            if (response.success) {
                resultElement.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-800">${response.message}</span>
                        </div>
                    </div>
                `;
                
                if (statusElement) {
                    statusElement.className = 'connection-status connected';
                    statusElement.textContent = 'Connected';
                }
            } else {
                resultElement.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span class="text-red-800">${response.message || response.error}</span>
                        </div>
                    </div>
                `;
                
                if (statusElement) {
                    statusElement.className = 'connection-status disconnected';
                    statusElement.textContent = 'Connection Failed';
                }
            }
        }
    } catch (error) {
        console.error('Error testing Square connection:', error);
        
        const statusElement = document.getElementById('square-status');
        const resultElement = document.getElementById('square-test-result');
        
        if (statusElement) {
            statusElement.className = 'connection-status disconnected';
            statusElement.textContent = 'Connection Failed';
        }
        
        if (resultElement) {
            resultElement.classList.remove('hidden');
            resultElement.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        <span class="text-red-800">Connection test failed: ${error.message}</span>
                    </div>
                </div>
            `;
        }
    }
}

/**
 * Save all settings
 */
async function saveAllSettings() {
    try {
        const settings = [];
        
        // Square settings
        const squareAppId = document.getElementById('square-app-id')?.value;
        const squareAccessToken = document.getElementById('square-access-token')?.value;
        const squareEnvironment = document.getElementById('square-environment')?.value;
        
        if (squareAppId) settings.push({ key: 'application_id', value: squareAppId, category: 'square' });
        if (squareAccessToken) settings.push({ key: 'access_token', value: squareAccessToken, category: 'square' });
        if (squareEnvironment) settings.push({ key: 'environment', value: squareEnvironment, category: 'square' });
        
        // Business settings
        const laborRate = document.getElementById('default-labor-rate')?.value;
        const consignmentSplit = document.getElementById('default-consignment-split')?.value;
        const businessName = document.getElementById('business-name')?.value;
        const locations = document.getElementById('default-locations')?.value;
        
        if (laborRate) settings.push({ key: 'default_labor_rate', value: laborRate, category: 'business' });
        if (consignmentSplit) settings.push({ key: 'default_consignment_split', value: consignmentSplit, category: 'business' });
        if (businessName) settings.push({ key: 'business_name', value: businessName, category: 'business' });
        if (locations) settings.push({ key: 'default_locations', value: locations, category: 'business' });
        
        // Advanced settings
        const cacheTimeout = document.getElementById('cache-timeout')?.value;
        const autoSyncInterval = document.getElementById('auto-sync-interval')?.value;
        const debugMode = document.getElementById('debug-mode')?.checked;
        
        if (cacheTimeout) settings.push({ key: 'cache_timeout', value: cacheTimeout, category: 'advanced' });
        if (autoSyncInterval) settings.push({ key: 'auto_sync_interval', value: autoSyncInterval, category: 'advanced' });
        settings.push({ key: 'debug_mode', value: debugMode.toString(), category: 'advanced' });
        
        const response = await apiRequest('/settings/api/settings', {
            method: 'POST',
            body: JSON.stringify(settings)
        });
        
        if (response.success) {
            showSuccess('Settings saved successfully!');
        } else {
            showWarning('Error saving settings: ' + (response.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showWarning('Error saving settings: ' + error.message);
    }
}

/**
 * Export table
 */
function exportTable(tableName) {
    window.open(`/settings/api/settings/export/${tableName}`, '_blank');
}

/**
 * Create backup
 */
async function createBackup() {
    try {
        const response = await apiRequest('/settings/api/settings/database/backup', {
            method: 'POST'
        });
        
        if (response.success) {
            showSuccess(`Backup created: ${response.backup_file}`);
        } else {
            showWarning('Error creating backup: ' + (response.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error creating backup:', error);
        showWarning('Error creating backup: ' + error.message);
    }
}

/**
 * Show restore modal
 */
function showRestoreModal() {
    showWarning('Restore functionality coming soon!');
}

/**
 * Clear cache
 */
function clearCache() {
    if (confirm('Are you sure you want to clear all cached data?')) {
        // Clear local cache
        if (typeof cache !== 'undefined') {
            cache = {};
        }
        
        showSuccess('Cache cleared successfully!');
    }
}

/**
 * Reset settings
 */
function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values? This cannot be undone.')) {
        showWarning('Reset settings functionality coming soon!');
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Square settings change
    const squareInputs = ['square-app-id', 'square-access-token', 'square-environment'];
    squareInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateSquareStatus);
        }
    });
});
