#!/usr/bin/env python3
"""
Market Stall Business Tracker - FIXED VERSION
"""

from flask import Flask, render_template, jsonify
from flask_cors import CORS
import sqlite3
import os

app = Flask(__name__)
CORS(app)

DATABASE = 'market_stall_dev.db'

def get_db():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    return """
    <html>
    <head><title>Market Stall Tracker</title></head>
    <body>
        <h1>🏪 Market Stall Tracker</h1>
        <p>✅ App is running successfully!</p>
        <h2>API Endpoints:</h2>
        <ul>
            <li><a href="/api/test">/api/test</a> - Test endpoint</li>
            <li><a href="/api/sales/summary">/api/sales/summary</a> - Sales summary</li>
            <li><a href="/api/products">/api/products</a> - Products list</li>
        </ul>
        <h2>Next Steps:</h2>
        <ol>
            <li>Test the endpoints above</li>
            <li>Run the full app: <code>python app.py</code></li>
            <li>Access the dashboard at: <code>http://localhost:5000</code></li>
        </ol>
    </body>
    </html>
    """

@app.route('/api/test')
def api_test():
    return jsonify({'status': 'success', 'message': 'API is working!'})

@app.route('/api/sales/summary')
def sales_summary():
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Get basic sales stats
        cursor.execute("""
            SELECT 
                COUNT(*) as total_sales,
                SUM(total_amount) as total_revenue,
                SUM(COALESCE(gross_profit, 0)) as total_profit
            FROM sales
        """)
        
        stats = dict(cursor.fetchone())
        db.close()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/products')
def products_list():
    try:
        db = get_db()
        cursor = db.cursor()
        
        cursor.execute('SELECT * FROM products LIMIT 10')
        products = [dict(row) for row in cursor.fetchall()]
        db.close()
        
        return jsonify({
            'success': True,
            'data': products
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 Starting Market Stall Tracker (Fixed Version)")
    print("📊 Database:", os.path.abspath(DATABASE))
    print("🌐 Access at: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
