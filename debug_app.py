#!/usr/bin/env python3
"""
Debug script to test the application initialization
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all imports step by step"""
    print("🔍 Testing imports...")
    
    try:
        print("✅ Testing Flask import...")
        from flask import Flask
        print("✅ Flask imported successfully")
        
        print("✅ Testing Flask-CORS import...")
        from flask_cors import CORS
        print("✅ Flask-CORS imported successfully")
        
        print("✅ Testing config import...")
        from config import config
        print("✅ Config imported successfully")
        
        print("✅ Testing database module...")
        from app.models.database import init_db, get_db
        print("✅ Database module imported successfully")
        
        print("✅ Testing sales model...")
        from app.models.sales import Sale
        print("✅ Sales model imported successfully")
        
        print("✅ Testing products model...")
        from app.models.products import Product
        print("✅ Products model imported successfully")
        
        print("✅ Testing analytics service...")
        from app.services.analytics_service import AnalyticsService
        print("✅ Analytics service imported successfully")
        
        print("✅ Testing blueprint imports...")
        from app.routes.dashboard import dashboard_bp
        from app.routes.sales import sales_bp
        from app.routes.analytics import analytics_bp
        from app.routes.inventory import inventory_bp
        from app.routes.settings import settings_bp
        print("✅ All blueprints imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def test_app_creation():
    """Test creating the Flask app"""
    print("\n🔍 Testing app creation...")
    
    try:
        from app import create_app
        
        print("✅ Creating app...")
        app = create_app()
        print(f"✅ App created successfully: {app}")
        
        print(f"✅ Database path: {app.config.get('DATABASE_PATH')}")
        
        return True
        
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """Test database initialization"""
    print("\n🔍 Testing database...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            from app.models.database import get_db
            db = get_db()
            
            # Test a simple query
            cursor = db.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"✅ Database tables: {[t[0] for t in tables]}")
            
            # Test if we can query sales
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            print(f"✅ Sales records: {sales_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Market Stall Tracker Debug Script")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    if imports_ok:
        # Test app creation
        app_ok = test_app_creation()
        
        if app_ok:
            # Test database
            db_ok = test_database()
            
            if db_ok:
                print("\n🎉 All tests passed! The app should work.")
                print("Try running: python app.py")
            else:
                print("\n❌ Database issues found.")
        else:
            print("\n❌ App creation issues found.")
    else:
        print("\n❌ Import issues found.")
    
    print("=" * 50)
