# Market Stall Business Tracker - Foundation Document

## 🎯 CORE PURPOSE

This is a **simple, effective business intelligence tool** for handmaker market stall operators. It answers one key question: **"What should I make more of to increase profit?"**

### Primary User
- **Nick**: Runs a market stall selling handmade products
- **Partner**: Will manage consignment locations  
- **Products**: ~180 handmade items with varying costs (materials + labor + overhead)
- **Locations**: Main stall + multiple consignment outlets

---

## 🏗️ DESIGN PRINCIPLES (CRITICAL - READ FIRST)

### ✅ KEEP IT SIMPLE
- **No over-engineering** - If Square already provides a feature, don't duplicate it
- **Focused functionality** - Solve specific market stall problems, not general business problems
- **Quick wins** - User should get value in under 5 minutes of setup

### ✅ PROFIT-FOCUSED (NOT REVENUE-FOCUSED)  
- **Everything shows profit**, not just revenue
- **COGS tracking is essential** - materials, labor, overhead, packaging
- **Profit margins drive decisions** - "This product makes me $5 profit per unit"

### ✅ MODULAR & CLEAN
- **Each feature is self-contained** - can be modified without breaking others
- **Clear separation**: Routes → Services → Models → Database
- **Template inheritance** - consistent UI without code duplication

### ❌ WHAT NOT TO BUILD
- **Don't replicate Square Dashboard** - they already show revenue, transaction counts, etc.
- **Don't build complex analytics** - focus on actionable insights only
- **Don't over-complicate COGS** - keep it simple: materials + labor + overhead = total cost
- **Don't add features "just because"** - every feature must solve a real problem

---

## 🛠️ TECHNICAL ARCHITECTURE

### Backend Structure
```
app/
├── models/          # Data layer
│   ├── database.py  # SQLite connection & migrations  
│   ├── sales.py     # Sale transactions with profit calculations
│   └── products.py  # Product catalog with detailed COGS
├── routes/          # API endpoints (Flask blueprints)
│   ├── dashboard.py # Main dashboard + summary APIs
│   ├── sales.py     # Sales entry & retrieval 
│   ├── analytics.py # Time comparisons & insights
│   ├── inventory.py # Product management & COGS
│   └── settings.py  # Square integration & config
└── services/        # Business logic
    ├── analytics_service.py  # Profit calculations & insights
    └── square_integration.py # EFTPOS data import
```

### Frontend Structure (SPA-style with templates)
```
templates/
├── base.html           # Shared layout + navigation
├── dashboard.html      # ✅ COMPLETE - Main dashboard
├── sales.html          # ❌ NEEDS BUILDING - Manual sales entry
├── analytics.html      # ❌ NEEDS BUILDING - Time comparisons  
├── inventory.html      # ❌ NEEDS BUILDING - COGS management
├── settings.html       # ❌ NEEDS BUILDING - Square integration
└── components/
    └── modals.html     # Reusable modal components
```

### Database Schema (Profit-Focused)
```sql
-- Sales with full profit tracking
sales: id, date, product_name, quantity, total_amount, 
       total_cogs, gross_profit, profit_margin, location

-- Products with detailed COGS breakdown  
products: id, name, raw_materials_cost, assembly_parts_cost,
          labor_hours, labor_rate, overhead_cost, packaging_cost,
          is_consignment, consignment_partner, consignment_split

-- Context for decision making
daily_notes: date, weather, foot_traffic, notes
settings: key, value, category (Square config, business settings)
```

---

## 📊 KEY FEATURES (CURRENT STATE)

### ✅ WORKING
- **Clean Architecture** - Properly refactored and organized
- **Database Schema** - Complete with profit tracking columns
- **Dashboard Backend** - APIs return profit-focused data
- **COGS Models** - Full support for handmaker cost tracking
- **Square Integration** - Backend logic for EFTPOS import
- **Auto-browser Opening** - Starts on http://localhost:5001


### 🎨 UI/UX Guidelines
- **TailwindCSS** for styling (already configured)
- **Chart.js** for visualizations (already loaded)
- **Consistent navigation** (sidebar works, just need page content)
- **Mobile-responsive** design
- **No unnecessary animations** - focus on speed and clarity

---

## 🚀 IMPLEMENTATION ROADMAP

### Phase 1: Complete Core Frontend (NEXT)
1. **Sales Entry Page** - Form for manual sales with real-time profit calculation
2. **Analytics Page** - Time period selectors and comparison charts  
3. **Inventory Page** - Product list + COGS management modal
4. **Settings Page** - Square API setup and testing interface

### Phase 2: Smart Features
1. **Inventory Velocity Tracking** - "You usually sell 5 per week, but sold 9 this week"
2. **Time-based Insights** - "Saturdays are your best profit days"
3. **Product Recommendations** - "Focus on making more X - high profit margin"

### Phase 3: Consignment Management  
1. **Location-based Sales Tracking** - Same products, different locations
2. **Partner Performance** - "Partner A location sells 3x faster than Partner B"
3. **Profit Split Calculations** - "You keep 60%, partner keeps 40%"

---

## 🎯 SUCCESS METRICS

### User Should Be Able To:
1. **Record a sale in under 30 seconds** (manual entry)
2. **See profit margins immediately** (not just revenue)
3. **Compare time periods easily** ("this month vs last month")
4. **Identify best/worst products by profit** (not just sales volume)
5. **Set product costs simply** (materials + labor + overhead)
6. **Import Square EFTPOS data reliably** (when configured)

### Code Quality Goals:
- **Each page loads in under 2 seconds**
- **No JavaScript errors in console**
- **Mobile responsive on phone/tablet**
- **New features can be added without breaking existing ones**

---

## 🔧 DEVELOPMENT GUIDELINES

### For Any AI Working On This Project:

#### ✅ DO:
- **Keep functions under 50 lines** where possible
- **Use the existing database models** (don't recreate the wheel)
- **Follow the established patterns** (look at dashboard.html for template structure)
- **Test with real data** (create sample sales to verify calculations)
- **Focus on profit, not revenue** (gross_profit and profit_margin are key fields)

#### ❌ DON'T:
- **Don't rebuild what's working** (dashboard backend is solid)
- **Don't add complex features** without justification
- **Don't break the modular structure** (keep routes, services, models separate)
- **Don't ignore mobile responsiveness** 
- **Don't create new database tables** without good reason

#### 🧪 TESTING APPROACH:
- **Create sample products** with known COGS
- **Add test sales** and verify profit calculations are correct
- **Test on mobile device** to ensure responsive design
- **Verify Square integration** doesn't break when adding features

---

## 💡 CORE PHILOSOPHY

> **"Simple tools for smart decisions"**

This isn't trying to be Salesforce or QuickBooks. It's a focused tool that helps a market stall operator answer:

- **"Which products make me the most money?"**
- **"When should I be at which location?"**  
- **"What should I make more of for next week?"**
- **"How much profit did I actually make this month?"**

Every feature should directly answer one of these questions. If it doesn't, question whether it belongs in this tool.

---

## 📝 CURRENT STATUS (as of refactoring completion)

- ✅ **Backend: Complete and working**
- ✅ **Database: Properly structured with profit tracking**  
- ✅ **Dashboard: Frontend and backend complete**
- ❌ **Other Pages: Only navigation exists, content needs building**
- ✅ **Architecture: Clean, modular, maintainable**

**Next task: Build the 4 missing frontend pages using the existing backend APIs.**

---

*This document should be read by any AI before making changes to ensure consistency with the project's core vision and principles.*